"""
Performance and benchmark tests for Enhanced Orchestrator - Phase A2

This module contains performance tests to validate orchestrator behavior
under load and stress conditions.
"""

import pytest
import asyncio
import time
import statistics
from unittest.mock import patch, AsyncMock, Mock
from concurrent.futures import ThreadPoolExecutor

from ai_coding_agent.orchestrator import (
    EnhancedOrchestrator,
    TaskContext,
    TaskType,
    TaskComplexity
)


@pytest.fixture
def performance_config():
    """Configuration optimized for performance testing."""
    return {
        "providers": {
            "ollama": {
                "models": {
                    f"test-model-{i}": {
                        "role": "test",
                        "agents": ["test_agent"],
                        "description": f"Test model {i}"
                    } for i in range(5)  # 5 test models
                }
            }
        },
        "routing": {
            "test_agent": {
                "primary": "test-model-0",
                "secondary": "test-model-1",
                "fallback": "test-model-2"
            }
        },
        "load_balancing": {
            "strategy": "least_loaded",
            "health_weight": 0.4,
            "performance_weight": 0.3,
            "availability_weight": 0.3
        },
        "model_analytics": {
            "track_performance": True,
            "track_quality_scores": True
        },
        "performance_settings": {
            "request_timeout": 30,
            "retry_attempts": 2,
            "max_concurrent_requests": 10
        }
    }


class TestOrchestatorPerformance:
    """Test orchestrator performance characteristics."""

    @pytest.mark.asyncio
    async def test_single_request_latency(self, performance_config):
        """Test latency of single request processing."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = performance_config["routing"]
            mock_config_instance.get_performance_settings.return_value = performance_config["performance_settings"]
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = {"response": "Test response"}
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()
                context = TaskContext()

                # Measure single request latency
                start_time = time.time()

                result = await orchestrator.dispatch_to_agent(
                    agent_name="test_agent",
                    task="Simple test task",
                    context=context,
                    task_type=TaskType.CODE_GENERATION,
                    complexity=TaskComplexity.SIMPLE
                )

                end_time = time.time()
                latency = end_time - start_time

                # Verify result and performance
                assert result.status == "success"
                assert latency < 1.0  # Should be very fast with mocked client

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_concurrent_request_throughput(self, performance_config):
        """Test throughput with concurrent requests."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = performance_config["routing"]
            mock_config_instance.get_performance_settings.return_value = performance_config["performance_settings"]
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()

                # Simulate variable response times
                async def mock_post(*args, **kwargs):
                    await asyncio.sleep(0.1)  # Simulate network delay
                    response = Mock()
                    response.status_code = 200
                    response.json.return_value = {"response": "Test response"}
                    return response

                mock_client.post = mock_post
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()
                context = TaskContext()

                # Create multiple concurrent requests
                num_requests = 20
                start_time = time.time()

                tasks = []
                for i in range(num_requests):
                    task = orchestrator.dispatch_to_agent(
                        agent_name="test_agent",
                        task=f"Test task {i}",
                        context=context,
                        task_type=TaskType.CODE_GENERATION,
                        complexity=TaskComplexity.SIMPLE
                    )
                    tasks.append(task)

                # Wait for all tasks to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)

                end_time = time.time()
                total_time = end_time - start_time

                # Analyze results
                successful_results = [r for r in results if hasattr(r, 'status') and r.status == "success"]
                throughput = len(successful_results) / total_time

                assert len(successful_results) == num_requests
                assert throughput > 10  # Should handle at least 10 requests/second

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_load_balancing_distribution(self, performance_config):
        """Test load balancing distribution under concurrent load."""
        # Configure multiple models for load balancing
        performance_config["routing"]["test_agent"]["task_routing"] = {
            "code_generation": "test-model-0"
        }

        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = performance_config["routing"]
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock()
                mock_response.status_code = 200
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()

                # Make many routing requests
                model_selections = []
                for _ in range(100):
                    model = await orchestrator.route_model_by_task(
                        "test_agent", TaskType.CODE_GENERATION, TaskComplexity.SIMPLE
                    )
                    model_selections.append(model)
                    orchestrator.load_balancer.record_request(model)

                # Analyze distribution
                from collections import Counter
                distribution = Counter(model_selections)

                # Should have some distribution across models (not all the same)
                assert len(distribution) >= 1

                # Get load balancing status
                lb_status = await orchestrator.get_load_balancing_status()
                assert "current_loads" in lb_status

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, performance_config):
        """Test memory usage characteristics under sustained load."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = performance_config["routing"]
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = {"response": "Test response"}
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()
                context = TaskContext()

                # Generate sustained load
                for i in range(200):  # Many requests
                    try:
                        await orchestrator.dispatch_to_agent(
                            agent_name="test_agent",
                            task=f"Task {i}",
                            context=context,
                            task_type=TaskType.CODE_GENERATION,
                            complexity=TaskComplexity.SIMPLE
                        )
                    except Exception:
                        pass  # Continue even if some fail

                # Check memory usage through metrics
                metrics_count = len(orchestrator.model_metrics)
                assert metrics_count <= 10  # Should not grow unbounded

                # Check that deques have limited size
                for metrics in orchestrator.model_metrics.values():
                    assert len(metrics.response_times) <= 100
                    assert len(metrics.quality_scores) <= 100

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_error_recovery_performance(self, performance_config):
        """Test performance under error conditions."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = performance_config["routing"]
            mock_config_instance.get_performance_settings.return_value = performance_config["performance_settings"]
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()

                # Simulate 50% failure rate
                call_count = 0
                async def mock_post(*args, **kwargs):
                    nonlocal call_count
                    call_count += 1
                    if call_count % 2 == 0:  # Every other call fails
                        raise Exception("Simulated network error")
                    else:
                        response = Mock()
                        response.status_code = 200
                        response.json.return_value = {"response": "Success"}
                        return response

                mock_client.post = mock_post
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()
                context = TaskContext()

                # Measure performance with errors
                start_time = time.time()

                results = []
                for i in range(10):
                    try:
                        result = await orchestrator.dispatch_to_agent(
                            agent_name="test_agent",
                            task=f"Task {i}",
                            context=context
                        )
                        results.append(result)
                    except Exception:
                        pass

                end_time = time.time()
                total_time = end_time - start_time

                # Should complete in reasonable time even with errors
                assert total_time < 30  # 30 seconds max for 10 requests with retries

                # Some requests should succeed due to retries
                successful = [r for r in results if r.status == "success"]
                assert len(successful) > 0

                await orchestrator.close()


class TestScalabilityBenchmarks:
    """Benchmark tests for scalability analysis."""

    @pytest.mark.asyncio
    async def test_routing_performance_at_scale(self, performance_config):
        """Benchmark routing performance with large numbers of requests."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = performance_config["routing"]
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_response = Mock(status_code=200)
                mock_client.post.return_value = mock_response
                mock_client_class.return_value = mock_client

                orchestrator = EnhancedOrchestrator()

                # Benchmark routing performance
                num_routes = 1000
                start_time = time.time()

                for _ in range(num_routes):
                    await orchestrator.route_model_by_task(
                        "test_agent", TaskType.CODE_GENERATION, TaskComplexity.SIMPLE
                    )

                end_time = time.time()
                total_time = end_time - start_time
                routes_per_second = num_routes / total_time

                # Should handle routing very quickly
                assert routes_per_second > 500  # 500+ routes per second

                await orchestrator.close()

    @pytest.mark.asyncio
    async def test_analytics_performance(self, performance_config):
        """Benchmark analytics generation performance."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = {}
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            orchestrator = EnhancedOrchestrator()

            # Populate metrics with test data
            from ai_coding_agent.orchestrator import ModelPerformanceMetrics
            for i in range(50):  # 50 models with metrics
                model_name = f"test-model-{i}"
                metrics = ModelPerformanceMetrics(model_name)

                # Add performance data
                for _ in range(100):  # Full metrics history
                    metrics.add_performance_data(1.0, 0.8, True)

                orchestrator.model_metrics[model_name] = metrics

            # Benchmark analytics generation
            start_time = time.time()

            analytics = await orchestrator.get_analytics()

            end_time = time.time()
            analytics_time = end_time - start_time

            # Should generate analytics quickly even with lots of data
            assert analytics_time < 1.0  # Less than 1 second
            assert len(analytics["models"]) == 50

            await orchestrator.close()


class TestMemoryBenchmarks:
    """Benchmark memory usage patterns."""

    @pytest.mark.asyncio
    async def test_metrics_memory_bounds(self, performance_config):
        """Test that metrics memory usage is bounded."""
        with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
            mock_config_instance = Mock()
            mock_config_instance.config = performance_config
            mock_config_instance.get_routing_config.return_value = {}
            mock_config_instance.get_performance_settings.return_value = {}
            mock_config_instance.get_quality_thresholds.return_value = {}
            mock_config_class.return_value = mock_config_instance

            orchestrator = EnhancedOrchestrator()

            # Create metrics and add lots of data
            from ai_coding_agent.orchestrator import ModelPerformanceMetrics
            metrics = ModelPerformanceMetrics("test-model")

            # Add way more data than the deque maxlen
            for i in range(1000):
                metrics.add_performance_data(float(i), 0.8, True)

            # Verify memory bounds
            assert len(metrics.response_times) <= 100  # Deque maxlen
            assert len(metrics.quality_scores) <= 100  # Deque maxlen

            # Should contain only recent data
            assert min(metrics.response_times) >= 900  # Recent values

            await orchestrator.close()


if __name__ == "__main__":
    # Run performance tests with verbose output
    pytest.main([__file__, "-v", "-s", "--tb=short"])
