import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Editor } from '@monaco-editor/react';
import {
  FiSave,
  FiCopy,
  FiZap,
  FiMessageSquare,
  FiSettings,
  FiMaximize2,
  FiType,
  FiSearch
} from 'react-icons/fi';
import { EditorTab, AgentAction, AICodeAssistanceRequest } from '../../types/ide';
import { AgentRole } from '../../types/agents';

interface AICodeEditorProps {
  tab: EditorTab;
  onContentChange: (content: string) => void;
  onSave: () => void;
  theme: 'light' | 'dark';
  agentActions: AgentAction[];
  onRequestAIAssistance: (request: AICodeAssistanceRequest) => void;
  className?: string;
}

interface AgentActionTooltipProps {
  action: AgentAction;
  onApply: () => void;
  onDismiss: () => void;
}

const AgentActionTooltip: React.FC<AgentActionTooltipProps> = ({ action, onApply, onDismiss }) => {
  const getActionIcon = () => {
    switch (action.type) {
      case 'suggestion':
        return <FiZap size={14} />;
      case 'completion':
        return <FiType size={14} />;
      case 'refactor':
        return <FiSettings size={14} />;
      case 'fix':
        return <FiSearch size={14} />;
      default:
        return <FiZap size={14} />;
    }
  };

  const getConfidenceColor = () => {
    if (action.confidence >= 0.8) return 'text-green-400';
    if (action.confidence >= 0.6) return 'text-yellow-400';
    return 'text-orange-400';
  };

  return (
    <div className="absolute z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-lg p-3 max-w-xs">
      <div className="flex items-center space-x-2 mb-2">
        <span className="text-blue-400">{getActionIcon()}</span>
        <span className="text-sm font-medium text-white">{action.title}</span>
        <span className={`text-xs ${getConfidenceColor()}`}>
          {Math.round(action.confidence * 100)}%
        </span>
      </div>

      <p className="text-xs text-gray-300 mb-3">{action.description}</p>

      {action.code && (
        <div className="bg-gray-900 rounded p-2 mb-3">
          <code className="text-xs text-green-400 whitespace-pre-wrap">
            {action.code.length > 100 ? action.code.substring(0, 100) + '...' : action.code}
          </code>
        </div>
      )}

      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-400 capitalize">
          {action.agentRole} Agent
        </span>
        <div className="flex space-x-2">
          <button
            onClick={onDismiss}
            className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
          >
            Dismiss
          </button>
          <button
            onClick={onApply}
            className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  );
};

export const AICodeEditor: React.FC<AICodeEditorProps> = ({
  tab,
  onContentChange,
  onSave,
  theme,
  agentActions,
  onRequestAIAssistance,
  className = ''
}) => {
  const editorRef = useRef<any>(null);
  const [selectedText, setSelectedText] = useState('');
  const [cursorPosition, setCursorPosition] = useState({ line: 1, column: 1 });
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [visibleActions, setVisibleActions] = useState<AgentAction[]>([]);
  const [fontSize, setFontSize] = useState(14);
  const [wordWrap, setWordWrap] = useState<'on' | 'off'>('off');

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;

    // Configure editor options
    editor.updateOptions({
      fontSize,
      wordWrap,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      suggestOnTriggerCharacters: true,
      quickSuggestions: true,
      tabSize: 2,
      insertSpaces: true,
    });

    // Add selection change listener
    editor.onDidChangeCursorSelection((e: any) => {
      const selection = editor.getSelection();
      const selectedText = editor.getModel()?.getValueInRange(selection) || '';
      setSelectedText(selectedText);

      const position = editor.getPosition();
      setCursorPosition({ line: position.lineNumber, column: position.column });
    });

    // Add keyboard shortcuts
    editor.addCommand(editor.KeyMod.CtrlCmd | editor.KeyCode.KeyS, () => {
      onSave();
    });

    editor.addCommand(editor.KeyMod.CtrlCmd | editor.KeyCode.KeyK, () => {
      setShowAIAssistant(true);
    });
  };

  const handleContentChange = useCallback((value: string | undefined) => {
    if (value !== undefined) {
      onContentChange(value);
    }
  }, [onContentChange]);

  const requestAICompletion = () => {
    if (!editorRef.current) return;

    const model = editorRef.current.getModel();
    const position = editorRef.current.getPosition();
    const lineContent = model.getLineContent(position.lineNumber);
    const beforeCursor = lineContent.substring(0, position.column - 1);
    const afterCursor = lineContent.substring(position.column - 1);

    const request: AICodeAssistanceRequest = {
      type: 'completion',
      context: {
        filePath: tab.path,
        language: tab.language,
        selectedText,
        cursorPosition,
        surroundingCode: model.getValue(),
      },
      prompt: `Complete this code: ${beforeCursor}`,
      agentRole: AgentRole.DEVELOPER,
    };

    onRequestAIAssistance(request);
  };

  const requestAIExplanation = () => {
    if (!selectedText) return;

    const request: AICodeAssistanceRequest = {
      type: 'explanation',
      context: {
        filePath: tab.path,
        language: tab.language,
        selectedText,
        cursorPosition,
      },
      prompt: `Explain this code: ${selectedText}`,
      agentRole: AgentRole.REVIEWER,
    };

    onRequestAIAssistance(request);
  };

  const requestAIRefactor = () => {
    if (!selectedText) return;

    const request: AICodeAssistanceRequest = {
      type: 'refactor',
      context: {
        filePath: tab.path,
        language: tab.language,
        selectedText,
        cursorPosition,
      },
      prompt: `Refactor this code: ${selectedText}`,
      agentRole: AgentRole.DEVELOPER,
    };

    onRequestAIAssistance(request);
  };

  const applyAgentAction = (action: AgentAction) => {
    if (!editorRef.current || !action.code) return;

    const editor = editorRef.current;
    const model = editor.getModel();

    if (action.range) {
      // Replace specific range
      const range = {
        startLineNumber: action.range.startLine,
        startColumn: action.range.startColumn,
        endLineNumber: action.range.endLine,
        endColumn: action.range.endColumn,
      };

      model.pushEditOperations([], [{
        range,
        text: action.code,
      }], () => null);
    } else if (action.position) {
      // Insert at specific position
      const position = {
        lineNumber: action.position.line,
        column: action.position.column,
      };

      model.pushEditOperations([], [{
        range: {
          startLineNumber: position.lineNumber,
          startColumn: position.column,
          endLineNumber: position.lineNumber,
          endColumn: position.column,
        },
        text: action.code,
      }], () => null);
    } else {
      // Insert at cursor
      const position = editor.getPosition();
      model.pushEditOperations([], [{
        range: {
          startLineNumber: position.lineNumber,
          startColumn: position.column,
          endLineNumber: position.lineNumber,
          endColumn: position.column,
        },
        text: action.code,
      }], () => null);
    }

    // Remove applied action
    setVisibleActions(prev => prev.filter(a => a.id !== action.id));
  };

  // Update visible actions when agentActions change
  useEffect(() => {
    setVisibleActions(agentActions.filter(action =>
      !action.position ||
      (Math.abs(action.position.line - cursorPosition.line) <= 5)
    ));
  }, [agentActions, cursorPosition]);

  return (
    <div className={`h-full flex flex-col bg-gray-900 ${className}`}>
      {/* Editor Toolbar */}
      <div className="bg-gray-800 border-b border-gray-700 px-3 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-300">{tab.name}</span>
          {tab.modified && <span className="text-yellow-400 text-xs">●</span>}
          <span className="text-xs text-gray-500">
            Line {cursorPosition.line}, Column {cursorPosition.column}
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Font Size Controls */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setFontSize(prev => Math.max(10, prev - 1))}
              className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              A-
            </button>
            <span className="text-xs text-gray-400 px-1">{fontSize}</span>
            <button
              onClick={() => setFontSize(prev => Math.min(24, prev + 1))}
              className="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              A+
            </button>
          </div>

          {/* Word Wrap Toggle */}
          <button
            onClick={() => setWordWrap(prev => prev === 'on' ? 'off' : 'on')}
            className={`p-1 rounded ${wordWrap === 'on' ? 'text-blue-400' : 'text-gray-400'} hover:bg-gray-700`}
            title="Toggle Word Wrap"
          >
            <FiMaximize2 size={14} />
          </button>

          {/* AI Assistant Toggle */}
          <button
            onClick={() => setShowAIAssistant(!showAIAssistant)}
            className={`p-1 rounded ${showAIAssistant ? 'text-blue-400' : 'text-gray-400'} hover:bg-gray-700`}
            title="AI Assistant (Ctrl+K)"
          >
            <FiZap size={14} />
          </button>

          {/* Save Button */}
          <button
            onClick={onSave}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            title="Save (Ctrl+S)"
          >
            <FiSave size={14} />
          </button>
        </div>
      </div>

      {/* AI Assistant Panel */}
      {showAIAssistant && (
        <div className="bg-gray-800 border-b border-gray-700 p-3">
          <div className="flex items-center space-x-2 mb-2">
            <FiZap size={16} className="text-blue-400" />
            <span className="text-sm font-medium text-white">AI Assistant</span>
          </div>

          <div className="flex flex-wrap gap-2">
            <button
              onClick={requestAICompletion}
              className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Complete Code
            </button>

            {selectedText && (
              <>
                <button
                  onClick={requestAIExplanation}
                  className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Explain Selection
                </button>
                <button
                  onClick={requestAIRefactor}
                  className="px-3 py-1 text-xs bg-purple-600 text-white rounded hover:bg-purple-700"
                >
                  Refactor Selection
                </button>
                <button
                  onClick={() => navigator.clipboard.writeText(selectedText)}
                  className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  <FiCopy size={12} className="inline mr-1" />
                  Copy
                </button>
              </>
            )}
          </div>

          {selectedText && (
            <div className="mt-2 text-xs text-gray-400">
              Selected: {selectedText.length} characters
            </div>
          )}
        </div>
      )}

      {/* Monaco Editor */}
      <div className="flex-1 relative">
        <Editor
          height="100%"
          language={tab.language}
          value={tab.content}
          theme={theme === 'dark' ? 'vs-dark' : 'vs-light'}
          onChange={handleContentChange}
          onMount={handleEditorDidMount}
          options={{
            fontSize,
            wordWrap,
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
          }}
        />

        {/* Agent Action Overlays */}
        {visibleActions.map((action, index) => (
          <div
            key={action.id}
            style={{
              position: 'absolute',
              top: `${(action.position?.line || cursorPosition.line) * 19}px`,
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 1000,
            }}
          >
            <AgentActionTooltip
              action={action}
              onApply={() => applyAgentAction(action)}
              onDismiss={() => setVisibleActions(prev => prev.filter(a => a.id !== action.id))}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
