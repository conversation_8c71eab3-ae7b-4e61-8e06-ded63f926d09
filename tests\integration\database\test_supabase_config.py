#!/usr/bin/env python3
"""
Simple test script to check Supabase configuration and authentication.
"""

def check_supabase_config():
    """Check Supabase configuration."""
    print("🔍 Checking Supabase Configuration")
    print("=" * 40)

    try:
        from src.ai_coding_agent.config import settings
        print("✅ Configuration loaded successfully")

        # Check Supabase settings
        supabase_url = settings.supabase.url
        anon_key = settings.supabase.anon_key
        service_key = settings.supabase.service_role_key

        print(f"📊 Supabase Configuration:")
        print(f"  URL: {supabase_url[:50] + '...' if supabase_url else 'Not set'}")
        print(f"  Anon Key: {'Set' if anon_key else 'Not set'}")
        print(f"  Service Key: {'Set' if service_key else 'Not set'}")

        if supabase_url and anon_key:
            print("✅ Basic Supabase configuration is complete")
            return True
        else:
            print("❌ Supabase configuration is incomplete")
            return False

    except Exception as e:
        print(f"❌ Configuration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supabase_import():
    """Test Supabase service imports."""
    print("\n🧪 Testing Supabase Service Imports")
    print("=" * 40)

    try:
        from src.ai_coding_agent.services.supabase_auth import SupabaseAuthService
        print("✅ SupabaseAuthService imported successfully")

        auth_service = SupabaseAuthService()
        print("✅ SupabaseAuthService initialized successfully")

        return True

    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supabase_connection():
    """Test basic Supabase connection."""
    print("\n🔌 Testing Supabase Connection")
    print("=" * 40)

    try:
        from src.ai_coding_agent.services.supabase_auth import SupabaseAuthService

        auth_service = SupabaseAuthService()

        # Test basic connection by getting session info
        try:
            # This is a simple test - just check if we can access the client
            client = auth_service.client
            print("✅ Supabase client accessible")
            print(f"📊 Client URL: {client.supabase_url[:50]}...")
            return True

        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False

    except Exception as e:
        print(f"❌ Service initialization failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Supabase Authentication Test")
    print("=" * 50)

    results = []

    # Test 1: Configuration
    results.append(check_supabase_config())

    # Test 2: Imports
    results.append(test_supabase_import())

    # Test 3: Connection
    results.append(test_supabase_connection())

    # Summary
    print("\n📊 Test Summary")
    print("=" * 40)
    passed = sum(results)
    total = len(results)

    test_names = ["Configuration", "Service Import", "Connection"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {name}")

    print(f"\n🎯 Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All Supabase tests passed! Ready for authentication.")
    else:
        print("⚠️ Some tests failed. Check configuration and dependencies.")

    return passed == total

if __name__ == "__main__":
    success = main()
