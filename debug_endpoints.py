#!/usr/bin/env python3
"""
Debug API endpoints - Test individual endpoints to identify issues
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_agents_endpoint():
    """Test the agents endpoint directly."""
    try:
        print("🔍 Testing agents configuration import...")
        from ai_coding_agent.agents import AGENT_CONFIGS
        print(f"✅ Found {len(AGENT_CONFIGS)} agents")

        print("\n🔍 Testing agents endpoint logic...")
        agents = []
        for role, config in AGENT_CONFIGS.items():
            # Handle capabilities - they might be strings or enum values
            capabilities = []
            for cap in config.capabilities:
                if hasattr(cap, 'value'):
                    capabilities.append(cap.value)
                else:
                    capabilities.append(str(cap))

            agent_data = {
                "role": role.value,
                "model": config.model,
                "description": config.description,
                "capabilities": capabilities,
                "temperature": config.temperature,
                "max_tokens": config.max_tokens
            }
            agents.append(agent_data)
            print(f"  - {role.value}: {config.model}")

        print(f"\n✅ Agents endpoint would return {len(agents)} agents")
        return True

    except Exception as e:
        print(f"❌ Error in agents endpoint: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_orchestrator():
    """Test orchestrator initialization."""
    try:
        print("\n🔍 Testing orchestrator...")
        from ai_coding_agent.services.ai.orchestrator import AgentOrchestrator
        orchestrator = AgentOrchestrator()
        print("✅ Orchestrator created successfully")

        print("\n🔍 Testing agent health...")
        health = await orchestrator.get_agent_health()
        print(f"✅ Agent health check completed for {len(health)} agents")

        await orchestrator.close()
        print("✅ Orchestrator closed successfully")
        return True

    except Exception as e:
        print(f"❌ Error in orchestrator: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Debug AI Endpoints Test\n")

    success1 = await test_agents_endpoint()
    success2 = await test_orchestrator()

    if success1 and success2:
        print("\n🎉 All individual tests passed!")
        print("The API endpoints should work correctly.")
    else:
        print("\n❌ Some tests failed. Check the errors above.")

    return success1 and success2

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
