"""
Workspace directory structure creation and management.

This module handles the creation and validation of the AI Coding Agent
workspace directory structure across different platforms.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from ..platform import get_platform_paths, set_platform_permissions

logger = logging.getLogger(__name__)


@dataclass
class DirectoryMetadata:
    """Metadata for workspace directories."""
    created_at: str
    purpose: str
    version: str = "1.0"
    platform: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DirectoryMetadata':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class WorkspaceStructure:
    """Workspace directory structure configuration."""
    base_dir: Path
    projects_dir: Path
    active_dir: Path
    archived_dir: Path
    logs_dir: Path
    temp_dir: Path
    metadata_file: Path

    def __post_init__(self):
        """Ensure all paths are Path objects."""
        for field_name in ['base_dir', 'projects_dir', 'active_dir',
                          'archived_dir', 'logs_dir', 'temp_dir', 'metadata_file']:
            value = getattr(self, field_name)
            if isinstance(value, str):
                setattr(self, field_name, Path(value))


class WorkspaceStructureManager:
    """Manages workspace directory structure creation and validation."""

    def __init__(self):
        self.platform_paths = get_platform_paths()
        self.structure: Optional[WorkspaceStructure] = None

    def create_workspace_structure(self, force: bool = False) -> WorkspaceStructure:
        """
        Create the complete workspace directory structure.

        Args:
            force: Whether to recreate existing directories

        Returns:
            WorkspaceStructure: Created workspace structure

        Raises:
            RuntimeError: If structure creation fails
        """
        try:
            # Create workspace structure object
            metadata_file = self.platform_paths.base_dir / ".workspace_metadata.json"

            self.structure = WorkspaceStructure(
                base_dir=self.platform_paths.base_dir,
                projects_dir=self.platform_paths.projects_dir,
                active_dir=self.platform_paths.active_dir,
                archived_dir=self.platform_paths.archived_dir,
                logs_dir=self.platform_paths.logs_dir,
                temp_dir=self.platform_paths.temp_dir,
                metadata_file=metadata_file
            )

            # Create directories atomically
            self._create_directories_atomic(force)

            # Set proper permissions
            self._set_directory_permissions()

            # Create metadata
            self._create_metadata()

            logger.info(f"Workspace structure created successfully at: {self.structure.base_dir}")
            return self.structure

        except Exception as e:
            logger.error(f"Failed to create workspace structure: {e}")
            raise RuntimeError(f"Workspace structure creation failed: {e}")

    def _create_directories_atomic(self, force: bool) -> None:
        """Create all directories atomically (all-or-nothing)."""
        directories_to_create = [
            self.structure.base_dir,
            self.structure.projects_dir,
            self.structure.active_dir,
            self.structure.archived_dir,
            self.structure.logs_dir,
            self.structure.temp_dir
        ]

        # Check if any directories exist and force is False
        if not force:
            existing_dirs = [d for d in directories_to_create if d.exists()]
            if existing_dirs:
                logger.info(f"Workspace directories already exist: {[str(d) for d in existing_dirs]}")
                # Validate existing structure
                if self.validate_structure():
                    logger.info("Existing workspace structure is valid")
                    return
                else:
                    logger.warning("Existing workspace structure is invalid, recreating...")

        # Create all directories
        created_dirs = []
        try:
            for directory in directories_to_create:
                if not directory.exists() or force:
                    directory.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(directory)
                    logger.debug(f"Created directory: {directory}")

            logger.info(f"Successfully created {len(created_dirs)} directories")

        except Exception as e:
            # Cleanup on failure (rollback)
            logger.error(f"Directory creation failed, cleaning up: {e}")
            for directory in reversed(created_dirs):
                try:
                    if directory.exists() and directory.is_dir():
                        directory.rmdir()
                        logger.debug(f"Cleaned up directory: {directory}")
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup directory {directory}: {cleanup_error}")
            raise

    def _set_directory_permissions(self) -> None:
        """Set appropriate permissions for all workspace directories."""
        directories = [
            self.structure.base_dir,
            self.structure.projects_dir,
            self.structure.active_dir,
            self.structure.archived_dir,
            self.structure.logs_dir,
            self.structure.temp_dir
        ]

        for directory in directories:
            if directory.exists():
                success = set_platform_permissions(directory, owner_only=True)
                if not success:
                    logger.warning(f"Failed to set permissions for: {directory}")
                else:
                    logger.debug(f"Permissions set for: {directory}")

    def _create_metadata(self) -> None:
        """Create workspace metadata file."""
        try:
            from ..platform.detector import get_platform_info
            platform_info = get_platform_info()

            metadata = DirectoryMetadata(
                created_at=datetime.now().isoformat(),
                purpose="AI Coding Agent Workspace",
                version="1.0",
                platform=platform_info.platform_type.value
            )

            with open(self.structure.metadata_file, 'w') as f:
                json.dump(metadata.to_dict(), f, indent=2)

            logger.debug(f"Metadata created: {self.structure.metadata_file}")

        except Exception as e:
            logger.warning(f"Failed to create metadata: {e}")

    def validate_structure(self) -> bool:
        """
        Validate that the workspace structure is complete and correct.

        Returns:
            bool: True if structure is valid
        """
        if self.structure is None:
            logger.error("No workspace structure to validate")
            return False

        try:
            # Check all required directories exist
            required_dirs = [
                self.structure.base_dir,
                self.structure.projects_dir,
                self.structure.active_dir,
                self.structure.archived_dir,
                self.structure.logs_dir,
                self.structure.temp_dir
            ]

            missing_dirs = [d for d in required_dirs if not d.exists()]
            if missing_dirs:
                logger.error(f"Missing directories: {[str(d) for d in missing_dirs]}")
                return False

            # Check directories are actually directories
            non_dirs = [d for d in required_dirs if not d.is_dir()]
            if non_dirs:
                logger.error(f"Non-directory paths: {[str(d) for d in non_dirs]}")
                return False

            # Check basic permissions
            inaccessible_dirs = [d for d in required_dirs if not os.access(d, os.R_OK | os.W_OK)]
            if inaccessible_dirs:
                logger.error(f"Inaccessible directories: {[str(d) for d in inaccessible_dirs]}")
                return False

            logger.info("Workspace structure validation successful")
            return True

        except Exception as e:
            logger.error(f"Structure validation failed: {e}")
            return False

    def repair_structure(self) -> bool:
        """
        Attempt to repair a corrupted workspace structure.

        Returns:
            bool: True if repair was successful
        """
        try:
            logger.info("Attempting to repair workspace structure...")

            # Recreate the structure
            self.create_workspace_structure(force=True)

            # Validate the repaired structure
            if self.validate_structure():
                logger.info("Workspace structure repair successful")
                return True
            else:
                logger.error("Workspace structure repair failed validation")
                return False

        except Exception as e:
            logger.error(f"Workspace structure repair failed: {e}")
            return False


# Global structure manager instance
_structure_manager = WorkspaceStructureManager()


def create_workspace_structure(force: bool = False) -> WorkspaceStructure:
    """
    Create workspace structure using the global manager.

    Args:
        force: Whether to recreate existing directories

    Returns:
        WorkspaceStructure: Created workspace structure
    """
    return _structure_manager.create_workspace_structure(force)
