#!/usr/bin/env python3
"""
Test script for Phase J1: Cross-Platform Isolation Infrastructure

This script tests the core functionality of the platform detection,
workspace management, and virtual environment isolation features.
"""

import sys
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_coding_agent.platform import get_platform_info, get_platform_paths
from ai_coding_agent.workspace import create_workspace_structure, get_workspace_manager
from ai_coding_agent.environment import create_project_venv, validate_environment_isolation

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_platform_detection():
    """Test platform detection functionality."""
    print("\n=== Testing Platform Detection ===")
    try:
        platform_info = get_platform_info()
        print(f"✓ Platform detected: {platform_info.platform_type.value}")
        print(f"  System: {platform_info.system}")
        print(f"  Release: {platform_info.release}")
        print(f"  Python: {platform_info.python_version}")
        print(f"  64-bit: {platform_info.is_64bit}")
        return True
    except Exception as e:
        print(f"✗ Platform detection failed: {e}")
        return False


def test_platform_paths():
    """Test platform-specific path configuration."""
    print("\n=== Testing Platform Paths ===")
    try:
        paths = get_platform_paths()
        print(f"✓ Platform paths configured")
        print(f"  Base directory: {paths.base_dir}")
        print(f"  Projects directory: {paths.projects_dir}")
        print(f"  Active directory: {paths.active_dir}")
        print(f"  Archived directory: {paths.archived_dir}")
        print(f"  Logs directory: {paths.logs_dir}")
        print(f"  Temp directory: {paths.temp_dir}")
        return True
    except Exception as e:
        print(f"✗ Platform paths configuration failed: {e}")
        return False


def test_workspace_creation():
    """Test workspace structure creation."""
    print("\n=== Testing Workspace Creation ===")
    try:
        structure = create_workspace_structure()
        print(f"✓ Workspace structure created")
        print(f"  Base: {structure.base_dir}")
        print(f"  Projects: {structure.projects_dir}")

        # Verify directories exist
        if structure.base_dir.exists():
            print("  ✓ Base directory exists")
        if structure.active_dir.exists():
            print("  ✓ Active directory exists")
        if structure.archived_dir.exists():
            print("  ✓ Archived directory exists")
        if structure.logs_dir.exists():
            print("  ✓ Logs directory exists")
        if structure.temp_dir.exists():
            print("  ✓ Temp directory exists")

        return True
    except Exception as e:
        print(f"✗ Workspace creation failed: {e}")
        return False


def test_project_management():
    """Test project creation and management."""
    print("\n=== Testing Project Management ===")
    try:
        workspace_manager = get_workspace_manager()

        # Create a test project
        test_project_name = "test_project_j1"
        project_path = workspace_manager.create_project(
            test_project_name,
            "Test project for Phase J1 validation"
        )
        print(f"✓ Test project created: {project_path}")

        # List active projects
        active_projects = workspace_manager.list_active_projects()
        print(f"✓ Active projects: {active_projects}")

        if test_project_name in active_projects:
            print(f"  ✓ Test project found in active list")

        return True, test_project_name
    except Exception as e:
        print(f"✗ Project management failed: {e}")
        return False, None


def test_virtual_environment(project_name):
    """Test virtual environment creation."""
    print("\n=== Testing Virtual Environment ===")
    try:
        # Create virtual environment for test project
        venv_info = create_project_venv(project_name)
        print(f"✓ Virtual environment created for {project_name}")
        print(f"  Path: {venv_info.path}")
        print(f"  Python: {venv_info.python_executable}")
        print(f"  Pip: {venv_info.pip_executable}")
        print(f"  Version: {venv_info.python_version}")

        # Verify executables exist
        if venv_info.python_executable.exists():
            print("  ✓ Python executable exists")
        if venv_info.pip_executable.exists():
            print("  ✓ Pip executable exists")

        return True
    except Exception as e:
        print(f"✗ Virtual environment creation failed: {e}")
        return False


def main():
    """Run all Phase J1 tests."""
    print("Phase J1: Cross-Platform Isolation Infrastructure - Test Suite")
    print("=" * 60)

    tests_passed = 0
    total_tests = 5

    # Test 1: Platform Detection
    if test_platform_detection():
        tests_passed += 1

    # Test 2: Platform Paths
    if test_platform_paths():
        tests_passed += 1

    # Test 3: Workspace Creation
    if test_workspace_creation():
        tests_passed += 1

    # Test 4: Project Management
    project_success, test_project = test_project_management()
    if project_success:
        tests_passed += 1

    # Test 5: Virtual Environment (only if project creation succeeded)
    if test_project and test_virtual_environment(test_project):
        tests_passed += 1

    # Summary
    print(f"\n=== Test Summary ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        print("✓ All Phase J1 tests passed! Infrastructure is ready.")
        return 0
    else:
        print(f"✗ {total_tests - tests_passed} test(s) failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
