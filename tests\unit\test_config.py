"""
Test configuration management.

This module tests the configuration system to ensure proper
environment variable handling and validation.
"""

import os

import pytest
from pydantic import ValidationError

from ai_coding_agent.config import get_settings


def test_ai_settings_temperature_validation():
    """Test AI settings temperature validation."""
    # Store original value
    original_value = os.environ.get("AI_TEMPERATURE")

    # Test invalid temperature through app settings
    os.environ["AI_TEMPERATURE"] = "3.0"
    try:
        from ai_coding_agent.config import AISettings

        with pytest.raises(ValidationError):
            AISettings()
    finally:
        # Clean up
        if original_value is not None:
            os.environ["AI_TEMPERATURE"] = original_value
        else:
            os.environ.pop("AI_TEMPERATURE", None)

    # Test valid value
    os.environ["AI_TEMPERATURE"] = "0.8"
    try:
        from ai_coding_agent.config import AISettings

        ai_settings = AISettings()
        assert ai_settings.temperature == 0.8
    finally:
        # Clean up
        if original_value is not None:
            os.environ["AI_TEMPERATURE"] = original_value
        else:
            os.environ.pop("AI_TEMPERATURE", None)


def test_cors_origins_parsing():
    """Test CORS origins parsing."""
    # Test string input
    os.environ["CORS_ORIGINS"] = "http://localhost:3000,http://localhost:8080"
    settings = get_settings()

    assert isinstance(settings.cors_origins, list)
    assert len(settings.cors_origins) == 2
    assert "http://localhost:3000" in settings.cors_origins

    # Clean up
    os.environ.pop("CORS_ORIGINS", None)


def test_settings_caching():
    """Test that settings are properly cached."""
    settings1 = get_settings()
    settings2 = get_settings()

    # Should be the same instance due to caching
    assert settings1 is settings2


def test_default_values():
    """Test that default configuration values are set correctly."""
    settings = get_settings()

    assert settings.app_name == "AI Coding Agent"
    assert settings.version == "0.1.0"
    assert settings.environment == "testing"  # Set in conftest.py
    assert settings.host == "localhost"
    assert settings.port == 8000

    # AI settings defaults
    assert settings.ai.ollama_host == "http://localhost:11434"
    assert settings.ai.default_model == "mistral:7b-instruct-q4_0"
    assert settings.ai.code_generation_model == "yi-coder:1.5b"
    assert settings.ai.max_tokens == 4096

    # Database defaults
    assert settings.database.host == "localhost"
    assert settings.database.port == 5432
    assert settings.database.name == "ai_coding_agent"
    assert settings.database.user == "postgres"
