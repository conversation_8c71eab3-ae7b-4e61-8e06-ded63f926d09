"""
Dependency Engine Service
Implements Phase B2: Dependency Engine & Phase Locking functionality.
"""

from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set, Tuple, Any, cast, TYPE_CHECKING, Literal
from uuid import uuid4
import re
import json
import requests
import time
import logging
from functools import lru_cache, wraps
from collections import defaultdict

from ..utils.serialization import serialize_dependency_data, prepare_for_logging, to_json

from sqlalchemy.orm import Session, selectinload, joinedload
from sqlalchemy import select, and_, or_, Index, text
from sqlalchemy.sql import func
from fastapi import HTTPException
from fastapi import status as http_status

from ..models import (
    Project, Roadmap, Phase, Step, Task,
    TaskStatus, AgentType,
    DependencyCheckStatus, DependencyType, OverrideLevel,
    BlockingDependency, DependencyCheckResult, PhaseProgressionResult,
    DependencyOverride, DependencyValidationRequest, DependencyValidationResponse,
    StatusBubbleEvent, DependencyGraph,
    ConditionalDependency, ConditionEvaluationContext, ConditionEvaluationResult,
    ConditionType, SoftDependency, SoftDependencyResult,
    ExternalDependency, ApprovalDependency, TimeDependency,
    ExternalDependencyStatus, BatchDependencyCheckRequest, BatchDependencyCheckResponse,
    DependencyBottleneck, DependencyMetrics, DelayPrediction, DependencyAnalyticsReport,
    VisualizationNode, VisualizationEdge, DependencyGraphVisualization,
    GanttChartTask, GanttChart, DashboardWidget, DependencyDashboard,
    ExternalToolType, ExternalToolConfig, ExternalToolSyncStatus, ExternalToolSyncResult,
    JiraIssueMapping, GitHubIssueMapping, CICDPipelineMapping, ExternalToolIntegration,
)

# Conditional imports to avoid circular dependencies
if TYPE_CHECKING:
    from ..services.ai import AgentOrchestrator
    from ..agents import AgentRole

# Configure logging
logger = logging.getLogger(__name__)

# Performance monitoring decorator
def monitor_performance(operation_name: str):
    """Decorator to monitor performance of dependency operations."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(f"Operation {operation_name} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"Operation {operation_name} failed after {duration:.3f}s: {str(e)}")
                raise
        return wrapper
    return decorator


class DependencyCache:
    """Enhanced caching mechanism for dependency operations with TTL and metrics."""

    def __init__(self, default_ttl: int = 300):  # 5 minutes default TTL
        self.dependency_graph_cache: Dict[str, Tuple[DependencyGraph, float]] = {}
        self.check_result_cache: Dict[str, Tuple[DependencyCheckResult, float]] = {}
        self.condition_cache: Dict[str, Tuple[ConditionEvaluationResult, float]] = {}
        self.external_status_cache: Dict[str, Tuple[ExternalDependencyStatus, float]] = {}
        self.default_ttl = default_ttl
        self.cache_hits = defaultdict(int)
        self.cache_misses = defaultdict(int)
        self.cache_evictions = defaultdict(int)

    def _is_expired(self, timestamp: float, ttl: Optional[int] = None) -> bool:
        """Check if cache entry is expired."""
        ttl = ttl or self.default_ttl
        return time.time() - timestamp > ttl

    def get_dependency_graph(self, key: str) -> Optional[DependencyGraph]:
        """Get dependency graph from cache with TTL check."""
        if key in self.dependency_graph_cache:
            graph, timestamp = self.dependency_graph_cache[key]
            if not self._is_expired(timestamp):
                self.cache_hits['dependency_graph'] += 1
                return graph
            else:
                del self.dependency_graph_cache[key]
                self.cache_evictions['dependency_graph'] += 1

        self.cache_misses['dependency_graph'] += 1
        return None

    def set_dependency_graph(self, key: str, graph: DependencyGraph) -> None:
        """Set dependency graph in cache with timestamp."""
        self.dependency_graph_cache[key] = (graph, time.time())

    def get_check_result(self, key: str) -> Optional[DependencyCheckResult]:
        """Get check result from cache with TTL check."""
        if key in self.check_result_cache:
            result, timestamp = self.check_result_cache[key]
            if not self._is_expired(timestamp, ttl=60):  # Shorter TTL for check results
                self.cache_hits['check_result'] += 1
                return result
            else:
                del self.check_result_cache[key]
                self.cache_evictions['check_result'] += 1

        self.cache_misses['check_result'] += 1
        return None

    def set_check_result(self, key: str, result: DependencyCheckResult) -> None:
        """Set check result in cache with timestamp."""
        self.check_result_cache[key] = (result, time.time())

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        return {
            "hits": dict(self.cache_hits),
            "misses": dict(self.cache_misses),
            "evictions": dict(self.cache_evictions),
            "hit_rate": {
                cache_type: hits / (hits + self.cache_misses[cache_type]) if (hits + self.cache_misses[cache_type]) > 0 else 0
                for cache_type, hits in self.cache_hits.items()
            }
        }

    def clear_cache(self) -> None:
        """Clear all cache entries."""
        self.dependency_graph_cache.clear()
        self.check_result_cache.clear()
        self.condition_cache.clear()
        self.external_status_cache.clear()
        logger.info("Dependency cache cleared")

    def invalidate_cache(self, entity_id: str):
        """Invalidate cache entries related to an entity."""
        # Remove from check result cache
        keys_to_remove = [key for key in self.check_result_cache.keys() if entity_id in key]
        for key in keys_to_remove:
            del self.check_result_cache[key]

        # Clear condition cache for entity
        condition_keys_to_remove = [key for key in self.condition_cache.keys() if entity_id in key]
        for key in condition_keys_to_remove:
            del self.condition_cache[key]

        logger.info(f"Cache invalidated for entity: {entity_id}")

    def clear_all(self):
        """Clear all caches."""
        self.clear_cache()


class DependencyMonitor:
    """Monitoring and alerting for dependency operations."""

    def __init__(self):
        self.alerts = []
        self.metrics = defaultdict(list)
        self.thresholds = {
            'check_latency_ms': 1000,  # Alert if dependency check takes > 1s
            'cache_hit_rate': 0.7,     # Alert if cache hit rate < 70%
            'failure_rate': 0.1,       # Alert if failure rate > 10%
            'bottleneck_count': 5      # Alert if > 5 bottlenecks detected
        }

    def record_metric(self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record a metric value with optional tags."""
        timestamp = time.time()
        self.metrics[metric_name].append({
            'value': value,
            'timestamp': timestamp,
            'tags': tags or {}
        })

        # Check thresholds and generate alerts
        self._check_thresholds(metric_name, value, tags)

    def _check_thresholds(self, metric_name: str, value: float, tags: Optional[Dict[str, str]]):
        """Check if metric value exceeds thresholds and generate alerts."""
        if metric_name in self.thresholds:
            threshold = self.thresholds[metric_name]

            # Different comparison logic based on metric type
            should_alert = False
            if metric_name in ['check_latency_ms', 'failure_rate', 'bottleneck_count']:
                should_alert = value > threshold
            elif metric_name in ['cache_hit_rate']:
                should_alert = value < threshold

            if should_alert:
                alert = {
                    'metric': metric_name,
                    'value': value,
                    'threshold': threshold,
                    'timestamp': time.time(),
                    'tags': tags or {},
                    'severity': self._get_alert_severity(metric_name, value, threshold)
                }
                self.alerts.append(alert)
                logger.warning(f"Alert: {metric_name} = {value} (threshold: {threshold})")

    def _get_alert_severity(self, metric_name: str, value: float, threshold: float) -> str:
        """Determine alert severity based on how much the value exceeds threshold."""
        if metric_name == 'check_latency_ms':
            if value > threshold * 3:
                return 'critical'
            elif value > threshold * 2:
                return 'high'
            else:
                return 'medium'
        elif metric_name == 'failure_rate':
            if value > threshold * 2:
                return 'critical'
            else:
                return 'high'
        else:
            return 'medium'

    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get alerts from the last N hours."""
        cutoff_time = time.time() - (hours * 3600)
        return [alert for alert in self.alerts if alert['timestamp'] > cutoff_time]

    def get_metric_summary(self, metric_name: str, hours: int = 24) -> Dict[str, Any]:
        """Get summary statistics for a metric over the last N hours."""
        cutoff_time = time.time() - (hours * 3600)
        recent_values = [
            m['value'] for m in self.metrics.get(metric_name, [])
            if m['timestamp'] > cutoff_time
        ]

        if not recent_values:
            return {'count': 0}

        return {
            'count': len(recent_values),
            'min': min(recent_values),
            'max': max(recent_values),
            'avg': sum(recent_values) / len(recent_values),
            'latest': recent_values[-1] if recent_values else None
        }


class ConditionEvaluator:
    """Evaluates conditional dependencies."""

    def __init__(self):
        self.safe_operators = {
            '==', '!=', '<', '>', '<=', '>=',
            'and', 'or', 'not', 'in', 'not in'
        }

    def evaluate_condition(self, condition: str, context: ConditionEvaluationContext) -> bool:
        """Safely evaluate a condition expression."""
        try:
            # Create a safe evaluation environment
            safe_dict = {
                'platform': context.platform,
                'environment': context.environment,
                'feature_flags': context.feature_flags,
                'user_choices': context.user_choices,
                'code_context': context.code_context,
                'time_context': context.time_context,
                'custom_variables': context.custom_variables,
            }

            # Simple condition parsing for safety
            if self._is_safe_condition(condition):
                return eval(condition, {"__builtins__": {}}, safe_dict)
            else:
                raise ValueError(f"Unsafe condition: {condition}")

        except Exception as e:
            print(f"Error evaluating condition '{condition}': {str(e)}")
            return False

    def _is_safe_condition(self, condition: str) -> bool:
        """Check if condition is safe to evaluate."""
        # Basic safety check - only allow simple comparisons
        dangerous_patterns = [
            r'__', r'import', r'exec', r'eval', r'open', r'file',
            r'subprocess', r'os\.', r'sys\.', r'globals', r'locals'
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, condition, re.IGNORECASE):
                return False

        return True

    def evaluate_conditional_dependency(
        self,
        conditional_dep: ConditionalDependency,
        context: ConditionEvaluationContext
    ) -> ConditionEvaluationResult:
        """Evaluate a conditional dependency."""
        try:
            condition_met = self.evaluate_condition(conditional_dep.condition, context)
            evaluation_details = f"Condition '{conditional_dep.condition}' evaluated to {condition_met}"
            errors = []
        except Exception as e:
            condition_met = False
            evaluation_details = f"Error evaluating condition: {str(e)}"
            errors = [str(e)]

        return ConditionEvaluationResult(
            condition_id=conditional_dep.dependency_id,
            dependency_id=conditional_dep.dependency_id,
            condition_met=condition_met,
            evaluation_details=evaluation_details,
            context_used=context,
            errors=errors
        )


class DependencyEngine:
    """Core dependency checking and phase locking engine."""

    def __init__(self, db: Session, user_id: Optional[str] = None):
        self.db = db
        self.user_id = user_id
        self.cache = DependencyCache()
        self.condition_evaluator = ConditionEvaluator()
        self._ai_orchestrator = None
        self.performance_metrics = defaultdict(list)
        self.monitor = DependencyMonitor()

        # Configure database session for optimal performance
        self._configure_db_session()

    def _configure_db_session(self):
        """Configure database session for optimal performance."""
        try:
            # Enable query optimization
            self.db.execute(text("PRAGMA query_only = 0"))
            self.db.execute(text("PRAGMA cache_size = 10000"))
            logger.info("Database session configured for optimal performance")
        except Exception as e:
            logger.warning(f"Could not configure database optimizations: {e}")

    def _get_ai_orchestrator(self):
        """Lazy load AI orchestrator to avoid circular imports."""
        if self._ai_orchestrator is None:
            try:
                from ..services.ai.orchestrator import AgentOrchestrator, TaskRequest
                self._ai_orchestrator = AgentOrchestrator()
                self._task_request_class = TaskRequest
                logger.info("AI Orchestrator initialized successfully")
            except ImportError as e:
                # Handle case where AI services are not available
                logger.warning(f"AI services not available: {e}")
                self._ai_orchestrator = None
                self._task_request_class = None
        return self._ai_orchestrator

    @monitor_performance("ai_dependency_prediction")
    async def predict_dependencies_with_ai(self, task_description: str, context: Dict[str, Any]) -> List[str]:
        """Use AI to predict likely dependencies from task descriptions."""
        orchestrator = self._get_ai_orchestrator()
        if not orchestrator:
            logger.warning("AI orchestrator not available for dependency prediction")
            return []

        try:
            # Create a more detailed prompt for dependency prediction
            prompt = f"""
            Analyze the following task and predict its likely dependencies:

            Task Description: {task_description}
            Context: {json.dumps(context, indent=2)}

            Please identify:
            1. Technical dependencies (APIs, databases, services)
            2. Resource dependencies (files, configurations, credentials)
            3. Process dependencies (approvals, reviews, testing)
            4. Sequential dependencies (tasks that must complete first)

            Return a JSON list of dependency names with brief descriptions.
            """

            if self._task_request_class:
                task_request = self._task_request_class(
                    task_description=prompt,
                    context={"analysis_type": "dependency_prediction", "model_preference": "qwen2.5:3b"}
                )
                response = await orchestrator.execute_task(task_request)

                # Parse AI response
                response_content = response.result if hasattr(response, 'result') else str(response)
                if response_content:
                    predicted_deps = self._parse_ai_dependency_response(response_content)
                    logger.info(f"AI predicted {len(predicted_deps)} dependencies for task")
                    # Extract dependency names from the parsed response
                    return [dep.get('name', str(dep)) if isinstance(dep, dict) else str(dep) for dep in predicted_deps]

            return []

        except Exception as e:
            logger.error(f"Error in AI dependency prediction: {e}")
            return []

    @monitor_performance("check_task_dependencies")
    def can_start_task(self, task_id: str) -> DependencyCheckResult:
        """Check if a task can start based on its dependencies."""
        task = self._get_task_with_context(task_id)

        if not task:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )

        # Check if task is already completed or in progress
        if task.status in [TaskStatus.COMPLETED, TaskStatus.IN_PROGRESS]:
            return DependencyCheckResult(
                entity_id=task_id,
                entity_type=DependencyType.TASK,
                entity_name=task.name,
                status=DependencyCheckStatus.CAN_START,
                can_start=True,
                message=f"Task '{task.name}' is already {task.status}"
            )

        # Get all tasks in the roadmap for dependency resolution
        all_tasks = self._get_all_tasks_in_roadmap(task.step.phase.roadmap_id)

        # Check task dependencies
        blocking_deps = []
        warnings = []

        for dep_id in task.dependencies:
            if dep_id in all_tasks:
                dep_task = all_tasks[dep_id]
                if dep_task.status != TaskStatus.COMPLETED:
                    blocking_deps.append(BlockingDependency(
                        dependency_id=dep_id,
                        dependency_type=DependencyType.TASK,
                        dependency_name=dep_task.name,
                        current_status=dep_task.status,
                        required_status=TaskStatus.COMPLETED.value,
                        blocking_reason=f"Task '{dep_task.name}' must be completed before '{task.name}' can start",
                        estimated_completion=None
                    ))
            else:
                warnings.append(f"Dependency task '{dep_id}' not found in roadmap")

        # Check step-level dependencies
        step_blocking_deps = self._check_step_dependencies(task.step, all_tasks)
        blocking_deps.extend(step_blocking_deps)

        # Check phase-level dependencies
        phase_blocking_deps = self._check_phase_dependencies(task.step.phase)
        blocking_deps.extend(phase_blocking_deps)

        # Determine overall status
        can_start = len(blocking_deps) == 0
        status = DependencyCheckStatus.CAN_START if can_start else DependencyCheckStatus.BLOCKED
        override_level = OverrideLevel.NONE if can_start else OverrideLevel.DEVELOPER

        message = f"Task '{task.name}' can start" if can_start else f"Task '{task.name}' is blocked by {len(blocking_deps)} dependencies"

        return DependencyCheckResult(
            entity_id=task_id,
            entity_type=DependencyType.TASK,
            entity_name=task.name,
            status=status,
            can_start=can_start,
            blocking_dependencies=blocking_deps,
            warnings=warnings,
            override_level=override_level,
            message=message
        )

    def can_start_step(self, step_id: str) -> DependencyCheckResult:
        """Check if a step can start based on its dependencies."""
        step = self._get_step_with_context(step_id)

        if not step:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Step {step_id} not found"
            )

        # Check if step is already completed or in progress
        if step.status in [TaskStatus.COMPLETED, TaskStatus.IN_PROGRESS]:
            return DependencyCheckResult(
                entity_id=step_id,
                entity_type=DependencyType.STEP,
                entity_name=step.name,
                status=DependencyCheckStatus.CAN_START,
                can_start=True,
                message=f"Step '{step.name}' is already {step.status}"
            )

        # Get all tasks in the roadmap for dependency resolution
        all_tasks = self._get_all_tasks_in_roadmap(step.phase.roadmap_id)

        # Check step dependencies
        blocking_deps = self._check_step_dependencies(step, all_tasks)

        # Check phase-level dependencies
        phase_blocking_deps = self._check_phase_dependencies(step.phase)
        blocking_deps.extend(phase_blocking_deps)

        # Determine overall status
        can_start = len(blocking_deps) == 0
        status = DependencyCheckStatus.CAN_START if can_start else DependencyCheckStatus.BLOCKED
        override_level = OverrideLevel.NONE if can_start else OverrideLevel.DEVELOPER

        message = f"Step '{step.name}' can start" if can_start else f"Step '{step.name}' is blocked by {len(blocking_deps)} dependencies"

        return DependencyCheckResult(
            entity_id=step_id,
            entity_type=DependencyType.STEP,
            entity_name=step.name,
            status=status,
            can_start=can_start,
            blocking_dependencies=blocking_deps,
            override_level=override_level,
            message=message
        )

    def can_start_phase(self, phase_id: str) -> DependencyCheckResult:
        """Check if a phase can start based on its dependencies."""
        phase = self._get_phase_with_context(phase_id)

        if not phase:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Phase {phase_id} not found"
            )

        # Check if phase is already completed or in progress
        if phase.status in [TaskStatus.COMPLETED, TaskStatus.IN_PROGRESS]:
            return DependencyCheckResult(
                entity_id=phase_id,
                entity_type=DependencyType.PHASE,
                entity_name=phase.name,
                status=DependencyCheckStatus.CAN_START,
                can_start=True,
                message=f"Phase '{phase.name}' is already {phase.status}"
            )

        # Check phase dependencies
        blocking_deps = self._check_phase_dependencies(phase)

        # Determine overall status
        can_start = len(blocking_deps) == 0
        status = DependencyCheckStatus.CAN_START if can_start else DependencyCheckStatus.BLOCKED
        override_level = OverrideLevel.NONE if can_start else OverrideLevel.ADMIN

        message = f"Phase '{phase.name}' can start" if can_start else f"Phase '{phase.name}' is blocked by {len(blocking_deps)} dependencies"

        return DependencyCheckResult(
            entity_id=phase_id,
            entity_type=DependencyType.PHASE,
            entity_name=phase.name,
            status=status,
            can_start=can_start,
            blocking_dependencies=blocking_deps,
            override_level=override_level,
            message=message
        )

    def _get_task_with_context(self, task_id: str) -> Optional[Task]:
        """Get task with full context (step, phase, roadmap)."""
        return self.db.execute(
            select(Task)
            .options(
                selectinload(Task.step)
                .selectinload(Step.phase)
                .selectinload(Phase.roadmap)
            )
            .where(Task.id == task_id)
        ).scalar_one_or_none()

    def _get_step_with_context(self, step_id: str) -> Optional[Step]:
        """Get step with full context (phase, roadmap)."""
        return self.db.execute(
            select(Step)
            .options(
                selectinload(Step.phase)
                .selectinload(Phase.roadmap)
            )
            .where(Step.id == step_id)
        ).scalar_one_or_none()

    def _get_phase_with_context(self, phase_id: str) -> Optional[Phase]:
        """Get phase with full context (roadmap)."""
        return self.db.execute(
            select(Phase)
            .options(selectinload(Phase.roadmap))
            .where(Phase.id == phase_id)
        ).scalar_one_or_none()

    def _get_all_tasks_in_roadmap(self, roadmap_id: str) -> Dict[str, Task]:
        """Get all tasks in a roadmap indexed by task ID."""
        tasks = self.db.execute(
            select(Task)
            .join(Step)
            .join(Phase)
            .where(Phase.roadmap_id == roadmap_id)
        ).scalars().all()

        return {task.id: task for task in tasks}

    def _check_step_dependencies(self, step: Step, all_tasks: Dict[str, Task]) -> List[BlockingDependency]:
        """Check dependencies at the step level."""
        blocking_deps = []

        for dep_id in step.dependencies:
            # For now, assume step dependencies are other step IDs
            # In a more complex system, this could reference tasks or other entities
            dep_step = self.db.execute(
                select(Step).where(Step.id == dep_id)
            ).scalar_one_or_none()

            if dep_step and dep_step.status != TaskStatus.COMPLETED:
                blocking_deps.append(BlockingDependency(
                    dependency_id=dep_id,
                    dependency_type=DependencyType.STEP,
                    dependency_name=dep_step.name,
                    current_status=dep_step.status,
                    required_status=TaskStatus.COMPLETED.value,
                    blocking_reason=f"Step '{dep_step.name}' must be completed before '{step.name}' can start",
                    estimated_completion=None
                ))

        return blocking_deps

    def _check_phase_dependencies(self, phase: Phase) -> List[BlockingDependency]:
        """Check dependencies at the phase level."""
        blocking_deps = []

        for dep_id in phase.dependencies:
            # For now, assume phase dependencies are other phase IDs
            dep_phase = self.db.execute(
                select(Phase).where(Phase.id == dep_id)
            ).scalar_one_or_none()

            if dep_phase and dep_phase.status != TaskStatus.COMPLETED:
                blocking_deps.append(BlockingDependency(
                    dependency_id=dep_id,
                    dependency_type=DependencyType.PHASE,
                    dependency_name=dep_phase.name,
                    current_status=dep_phase.status,
                    required_status=TaskStatus.COMPLETED.value,
                    blocking_reason=f"Phase '{dep_phase.name}' must be completed before '{phase.name}' can start",
                    estimated_completion=None
                ))

        return blocking_deps

    def get_blocking_dependencies(self, entity_id: str, entity_type: DependencyType) -> List[BlockingDependency]:
        """Get all dependencies that are blocking an entity from starting."""
        if entity_type == DependencyType.TASK:
            result = self.can_start_task(entity_id)
        elif entity_type == DependencyType.STEP:
            result = self.can_start_step(entity_id)
        elif entity_type == DependencyType.PHASE:
            result = self.can_start_phase(entity_id)
        else:
            raise ValueError(f"Unsupported entity type: {entity_type}")

        return result.blocking_dependencies

    def validate_dependency_request(self, request: DependencyValidationRequest) -> DependencyValidationResponse:
        """Validate a dependency request and determine if operation is allowed."""
        validation_id = str(uuid4())

        # Get dependency check result
        if request.entity_type == DependencyType.TASK:
            check_result = self.can_start_task(request.entity_id)
        elif request.entity_type == DependencyType.STEP:
            check_result = self.can_start_step(request.entity_id)
        elif request.entity_type == DependencyType.PHASE:
            check_result = self.can_start_phase(request.entity_id)
        else:
            raise ValueError(f"Unsupported entity type: {request.entity_type}")

        # Determine if operation is allowed
        allowed = check_result.can_start
        requires_override = not allowed and not request.force_override
        override_applied = None
        warnings = []
        errors = []

        # Handle force override
        if request.force_override and not allowed:
            if not request.override_reason:
                errors.append("Override reason is required when force_override is True")
                allowed = False
            else:
                # Create override record
                override_applied = DependencyOverride(
                    entity_id=request.entity_id,
                    entity_type=request.entity_type,
                    override_level=check_result.override_level,
                    reason=request.override_reason,
                    requested_by=request.user_id,
                    approved_by=request.user_id,  # Self-approved for now
                    expires_at=None
                )
                allowed = True
                requires_override = False
                warnings.append(f"Dependency check overridden: {request.override_reason}")

        # Add warnings from dependency check
        warnings.extend(check_result.warnings)

        return DependencyValidationResponse(
            validation_id=validation_id,
            request=request,
            result=check_result,
            allowed=allowed,
            requires_override=requires_override,
            override_applied=override_applied,
            warnings=warnings,
            errors=errors
        )

    def check_phase_progression(self, phase_id: str) -> PhaseProgressionResult:
        """Check if a phase can progress to the next phase."""
        phase = self._get_phase_with_context(phase_id)

        if not phase:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Phase {phase_id} not found"
            )

        # Get all steps and tasks in this phase
        steps = self.db.execute(
            select(Step)
            .options(selectinload(Step.tasks))
            .where(Step.phase_id == phase_id)
            .order_by(Step.order_index)
        ).scalars().all()

        # Calculate completion statistics
        total_steps = len(steps)
        completed_steps = sum(1 for step in steps if step.status == TaskStatus.COMPLETED)

        total_tasks = sum(len(step.tasks) for step in steps)
        completed_tasks = sum(
            sum(1 for task in step.tasks if task.status == TaskStatus.COMPLETED)
            for step in steps
        )

        completion_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

        # Find blocking items
        blocking_items = []
        for step in steps:
            if step.status != TaskStatus.COMPLETED:
                step_blocking = {
                    "type": "step",
                    "id": step.id,
                    "name": step.name,
                    "status": step.status,
                    "blocking_tasks": []
                }

                for task in step.tasks:
                    if task.status != TaskStatus.COMPLETED:
                        step_blocking["blocking_tasks"].append({
                            "id": task.id,
                            "name": task.name,
                            "status": task.status,
                            "assigned_agent": task.assigned_agent
                        })

                blocking_items.append(step_blocking)

        # Check if phase can progress (all steps completed)
        can_progress = completed_steps == total_steps

        # Find next phase
        next_phase_id = None
        if can_progress:
            next_phase = self.db.execute(
                select(Phase)
                .where(
                    Phase.roadmap_id == phase.roadmap_id,
                    Phase.order_index > phase.order_index
                )
                .order_by(Phase.order_index)
                .limit(1)
            ).scalar_one_or_none()

            if next_phase:
                next_phase_id = next_phase.id

        return PhaseProgressionResult(
            phase_id=phase_id,
            phase_name=phase.name,
            can_progress=can_progress,
            completion_percentage=completion_percentage,
            completed_steps=completed_steps,
            total_steps=total_steps,
            completed_tasks=completed_tasks,
            total_tasks=total_tasks,
            blocking_items=blocking_items,
            next_phase_id=next_phase_id,
            estimated_completion=None
        )

    def validate_task_execution(self, task_id: str, operation: Literal["start", "complete", "skip"] = "start", force_override: bool = False, override_reason: Optional[str] = None) -> DependencyValidationResponse:
        """Validate if a task can be executed (started, completed, etc.)."""
        request = DependencyValidationRequest(
            entity_id=task_id,
            entity_type=DependencyType.TASK,
            operation=operation,
            force_override=force_override,
            override_reason=override_reason,
            user_id=self.user_id or "system"
        )
        return self.validate_dependency_request(request)

    def validate_step_execution(self, step_id: str, operation: Literal["start", "complete", "skip"] = "start", force_override: bool = False, override_reason: Optional[str] = None) -> DependencyValidationResponse:
        """Validate if a step can be executed (started, completed, etc.)."""
        request = DependencyValidationRequest(
            entity_id=step_id,
            entity_type=DependencyType.STEP,
            operation=operation,
            force_override=force_override,
            override_reason=override_reason,
            user_id=self.user_id or "system"
        )
        return self.validate_dependency_request(request)

    def validate_phase_execution(self, phase_id: str, operation: Literal["start", "complete", "skip"] = "start", force_override: bool = False, override_reason: Optional[str] = None) -> DependencyValidationResponse:
        """Validate if a phase can be executed (started, completed, etc.)."""
        request = DependencyValidationRequest(
            entity_id=phase_id,
            entity_type=DependencyType.PHASE,
            operation=operation,
            force_override=force_override,
            override_reason=override_reason,
            user_id=self.user_id or "system"
        )
        return self.validate_dependency_request(request)

    def enforce_phase_locking(self, entity_id: str, entity_type: DependencyType, operation: Literal["start", "complete", "skip"] = "start") -> Tuple[bool, List[str], List[str]]:
        """
        Enforce phase locking rules and return validation result.

        Returns:
            Tuple of (is_allowed, warnings, errors)
        """
        try:
            validation = self.validate_dependency_request(
                DependencyValidationRequest(
                    entity_id=entity_id,
                    entity_type=entity_type,
                    operation=operation,
                    force_override=False,
                    override_reason=None,
                    user_id=self.user_id or "system"
                )
            )

            return validation.allowed, validation.warnings, validation.errors

        except Exception as e:
            return False, [], [f"Validation error: {str(e)}"]

    def get_out_of_order_warnings(self, entity_id: str, entity_type: DependencyType) -> List[str]:
        """Generate warnings for out-of-order execution attempts."""
        warnings = []

        try:
            if entity_type == DependencyType.TASK:
                result = self.can_start_task(entity_id)
                task = self._get_task_with_context(entity_id)

                if task and not result.can_start:
                    # Check if there are earlier tasks in the same step that aren't completed
                    step_tasks = self.db.execute(
                        select(Task)
                        .where(Task.step_id == task.step_id)
                        .order_by(Task.order_index)
                    ).scalars().all()

                    for earlier_task in step_tasks:
                        if (earlier_task.order_index < task.order_index and
                            earlier_task.status != TaskStatus.COMPLETED):
                            warnings.append(
                                f"Task '{task.name}' is being started out of order. "
                                f"Earlier task '{earlier_task.name}' is not yet completed."
                            )

            elif entity_type == DependencyType.STEP:
                result = self.can_start_step(entity_id)
                step = self._get_step_with_context(entity_id)

                if step and not result.can_start:
                    # Check if there are earlier steps in the same phase that aren't completed
                    phase_steps = self.db.execute(
                        select(Step)
                        .where(Step.phase_id == step.phase_id)
                        .order_by(Step.order_index)
                    ).scalars().all()

                    for earlier_step in phase_steps:
                        if (earlier_step.order_index < step.order_index and
                            earlier_step.status != TaskStatus.COMPLETED):
                            warnings.append(
                                f"Step '{step.name}' is being started out of order. "
                                f"Earlier step '{earlier_step.name}' is not yet completed."
                            )

            elif entity_type == DependencyType.PHASE:
                result = self.can_start_phase(entity_id)
                phase = self._get_phase_with_context(entity_id)

                if phase and not result.can_start:
                    # Check if there are earlier phases in the same roadmap that aren't completed
                    roadmap_phases = self.db.execute(
                        select(Phase)
                        .where(Phase.roadmap_id == phase.roadmap_id)
                        .order_by(Phase.order_index)
                    ).scalars().all()

                    for earlier_phase in roadmap_phases:
                        if (earlier_phase.order_index < phase.order_index and
                            earlier_phase.status != TaskStatus.COMPLETED):
                            warnings.append(
                                f"Phase '{phase.name}' is being started out of order. "
                                f"Earlier phase '{earlier_phase.name}' is not yet completed."
                            )

        except Exception as e:
            warnings.append(f"Error checking execution order: {str(e)}")

        return warnings

    def create_status_bubble_event(self, source_entity_id: str, source_entity_type: DependencyType, old_status: str, new_status: str) -> StatusBubbleEvent:
        """Create a status bubble event for tracking status propagation."""
        return StatusBubbleEvent(
            source_entity_id=source_entity_id,
            source_entity_type=source_entity_type,
            old_status=old_status,
            new_status=new_status,
            user_id=self.user_id
        )

    def process_automatic_progression(self, entity_id: str, entity_type: DependencyType) -> List[StatusBubbleEvent]:
        """
        Process automatic progression when an entity is completed.
        Returns list of status bubble events for all triggered changes.
        """
        events = []

        try:
            if entity_type == DependencyType.TASK:
                events.extend(self._process_task_completion_progression(entity_id))
            elif entity_type == DependencyType.STEP:
                events.extend(self._process_step_completion_progression(entity_id))
            elif entity_type == DependencyType.PHASE:
                events.extend(self._process_phase_completion_progression(entity_id))

        except Exception as e:
            # Log error but don't fail the operation
            print(f"Error in automatic progression: {str(e)}")

        return events

    def _process_task_completion_progression(self, task_id: str) -> List[StatusBubbleEvent]:
        """Process progression when a task is completed."""
        events = []
        task = self._get_task_with_context(task_id)

        if not task or task.status != TaskStatus.COMPLETED:
            return events

        # Check if all tasks in the step are completed
        step_tasks = self.db.execute(
            select(Task).where(Task.step_id == task.step_id)
        ).scalars().all()

        all_tasks_completed = all(t.status == TaskStatus.COMPLETED for t in step_tasks)

        if all_tasks_completed and task.step.status != TaskStatus.COMPLETED:
            # Mark step as completed
            old_status = task.step.status
            task.step.status = TaskStatus.COMPLETED
            task.step.updated_at = datetime.now(timezone.utc)

            events.append(self.create_status_bubble_event(
                task.step.id, DependencyType.STEP, old_status, TaskStatus.COMPLETED
            ))

            # Process step completion progression
            events.extend(self._process_step_completion_progression(task.step.id))

        return events

    def _process_step_completion_progression(self, step_id: str) -> List[StatusBubbleEvent]:
        """Process progression when a step is completed."""
        events = []
        step = self._get_step_with_context(step_id)

        if not step or step.status != TaskStatus.COMPLETED:
            return events

        # Check if all steps in the phase are completed
        phase_steps = self.db.execute(
            select(Step).where(Step.phase_id == step.phase_id)
        ).scalars().all()

        all_steps_completed = all(s.status == TaskStatus.COMPLETED for s in phase_steps)

        if all_steps_completed and step.phase.status != TaskStatus.COMPLETED:
            # Mark phase as completed
            old_status = step.phase.status
            step.phase.status = TaskStatus.COMPLETED
            step.phase.updated_at = datetime.now(timezone.utc)

            events.append(self.create_status_bubble_event(
                step.phase.id, DependencyType.PHASE, old_status, TaskStatus.COMPLETED
            ))

            # Process phase completion progression
            events.extend(self._process_phase_completion_progression(step.phase.id))

        return events

    def _process_phase_completion_progression(self, phase_id: str) -> List[StatusBubbleEvent]:
        """Process progression when a phase is completed."""
        events = []
        phase = self._get_phase_with_context(phase_id)

        if not phase or phase.status != TaskStatus.COMPLETED:
            return events

        # Check if all phases in the roadmap are completed
        roadmap_phases = self.db.execute(
            select(Phase).where(Phase.roadmap_id == phase.roadmap_id)
        ).scalars().all()

        all_phases_completed = all(p.status == TaskStatus.COMPLETED for p in roadmap_phases)

        if all_phases_completed and phase.roadmap.status != TaskStatus.COMPLETED:
            # Mark roadmap as completed
            old_status = phase.roadmap.status
            phase.roadmap.status = TaskStatus.COMPLETED
            phase.roadmap.updated_at = datetime.now(timezone.utc)

            events.append(self.create_status_bubble_event(
                phase.roadmap.id, DependencyType.PHASE, old_status, TaskStatus.COMPLETED  # Using PHASE as roadmap type
            ))

        # Check if next phase can be automatically started
        next_phase = self.db.execute(
            select(Phase)
            .where(
                Phase.roadmap_id == phase.roadmap_id,
                Phase.order_index > phase.order_index
            )
            .order_by(Phase.order_index)
            .limit(1)
        ).scalar_one_or_none()

        if next_phase and next_phase.status == TaskStatus.PENDING:
            # Check if next phase can start
            can_start_result = self.can_start_phase(next_phase.id)

            if can_start_result.can_start:
                # Automatically start next phase
                old_status = next_phase.status
                next_phase.status = TaskStatus.IN_PROGRESS
                next_phase.updated_at = datetime.now(timezone.utc)

                events.append(self.create_status_bubble_event(
                    next_phase.id, DependencyType.PHASE, old_status, TaskStatus.IN_PROGRESS
                ))

        return events

    def create_override(self, entity_id: str, entity_type: DependencyType, override_level: OverrideLevel, reason: str, expires_at: Optional[datetime] = None) -> DependencyOverride:
        """Create a dependency override record."""
        return DependencyOverride(
            entity_id=entity_id,
            entity_type=entity_type,
            override_level=override_level,
            reason=reason,
            requested_by=self.user_id or "system",
            approved_by=self.user_id or "system",
            expires_at=expires_at
        )

    def check_override_permissions(self, override_level: OverrideLevel, user_id: str) -> bool:
        """Check if user has permission for the requested override level."""
        # For now, implement basic permission checking
        # In a real system, this would check user roles/permissions

        if override_level == OverrideLevel.NONE:
            return True
        elif override_level == OverrideLevel.WARNING:
            return True  # Anyone can override warnings
        elif override_level == OverrideLevel.DEVELOPER:
            return True  # For now, allow all users developer overrides
        elif override_level == OverrideLevel.ADMIN:
            return True  # For now, allow all users admin overrides

        return False

    def apply_override(self, entity_id: str, entity_type: DependencyType, reason: str, override_level: Optional[OverrideLevel] = None) -> DependencyOverride:
        """Apply an override to bypass dependency checks."""
        # Get current dependency check result to determine required override level
        if entity_type == DependencyType.TASK:
            check_result = self.can_start_task(entity_id)
        elif entity_type == DependencyType.STEP:
            check_result = self.can_start_step(entity_id)
        elif entity_type == DependencyType.PHASE:
            check_result = self.can_start_phase(entity_id)
        else:
            raise ValueError(f"Unsupported entity type: {entity_type}")

        # Use provided override level or the one from check result
        required_level = override_level or check_result.override_level

        # Check permissions
        if not self.check_override_permissions(required_level, self.user_id or "system"):
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions for {required_level.value} override"
            )

        # Create override record
        override = self.create_override(
            entity_id=entity_id,
            entity_type=entity_type,
            override_level=required_level,
            reason=reason
        )

        return override

    def is_override_active(self, entity_id: str, entity_type: DependencyType) -> bool:
        """Check if there's an active override for the given entity."""
        # In a real implementation, this would check a database table
        # For now, we'll return False as overrides are handled in-memory
        return False

    def get_active_overrides(self, entity_id: str, entity_type: DependencyType) -> List[DependencyOverride]:
        """Get all active overrides for an entity."""
        # In a real implementation, this would query a database table
        # For now, return empty list
        return []

    def revoke_override(self, entity_id: str, entity_type: DependencyType, reason: str) -> bool:
        """Revoke an active override."""
        # In a real implementation, this would update the database
        # For now, just return True
        return True

    def generate_dependency_warnings(self, entity_id: str, entity_type: DependencyType) -> List[str]:
        """Generate comprehensive warnings for dependency violations."""
        warnings = []

        try:
            # Get dependency check result
            if entity_type == DependencyType.TASK:
                check_result = self.can_start_task(entity_id)
            elif entity_type == DependencyType.STEP:
                check_result = self.can_start_step(entity_id)
            elif entity_type == DependencyType.PHASE:
                check_result = self.can_start_phase(entity_id)
            else:
                return warnings

            # Add warnings from dependency check
            warnings.extend(check_result.warnings)

            # Add out-of-order warnings
            warnings.extend(self.get_out_of_order_warnings(entity_id, entity_type))

            # Add blocking dependency warnings
            if check_result.blocking_dependencies:
                warnings.append(
                    f"Entity '{check_result.entity_name}' has {len(check_result.blocking_dependencies)} blocking dependencies"
                )

                for dep in check_result.blocking_dependencies:
                    warnings.append(
                        f"Blocked by {dep.dependency_type.value} '{dep.dependency_name}' "
                        f"(status: {dep.current_status}, required: {dep.required_status})"
                    )

            # Add override requirement warnings
            if check_result.override_level != OverrideLevel.NONE:
                warnings.append(
                    f"Override level '{check_result.override_level.value}' required to proceed"
                )

        except Exception as e:
            warnings.append(f"Error generating warnings: {str(e)}")

        return warnings

    def check_circular_dependencies(self, entity_id: str, entity_type: DependencyType) -> List[str]:
        """Check for circular dependencies and return warnings."""
        warnings = []

        try:
            # Build dependency graph starting from the entity
            visited = set()
            path = []

            def check_circular(current_id: str, current_type: DependencyType):
                if current_id in visited:
                    if current_id in path:
                        # Found a cycle
                        cycle_start = path.index(current_id)
                        cycle = path[cycle_start:] + [current_id]
                        warnings.append(f"Circular dependency detected: {' -> '.join(cycle)}")
                    return

                visited.add(current_id)
                path.append(current_id)

                # Get dependencies for current entity
                dependencies = []
                if current_type == DependencyType.TASK:
                    task = self._get_task_with_context(current_id)
                    if task:
                        dependencies = task.dependencies
                elif current_type == DependencyType.STEP:
                    step = self._get_step_with_context(current_id)
                    if step:
                        dependencies = step.dependencies
                elif current_type == DependencyType.PHASE:
                    phase = self._get_phase_with_context(current_id)
                    if phase:
                        dependencies = phase.dependencies

                # Recursively check dependencies
                for dep_id in dependencies:
                    check_circular(dep_id, current_type)  # Assume same type for simplicity

                path.pop()

            check_circular(entity_id, entity_type)

        except Exception as e:
            warnings.append(f"Error checking circular dependencies: {str(e)}")

        return warnings

    def validate_execution_order(self, entity_id: str, entity_type: DependencyType) -> Tuple[bool, List[str]]:
        """Validate execution order and return (is_valid, warnings)."""
        warnings = []
        is_valid = True

        try:
            # Check dependency violations
            dependency_warnings = self.generate_dependency_warnings(entity_id, entity_type)
            warnings.extend(dependency_warnings)

            # Check circular dependencies
            circular_warnings = self.check_circular_dependencies(entity_id, entity_type)
            warnings.extend(circular_warnings)

            # Determine if execution order is valid
            if entity_type == DependencyType.TASK:
                check_result = self.can_start_task(entity_id)
            elif entity_type == DependencyType.STEP:
                check_result = self.can_start_step(entity_id)
            elif entity_type == DependencyType.PHASE:
                check_result = self.can_start_phase(entity_id)
            else:
                is_valid = False
                warnings.append(f"Unsupported entity type: {entity_type}")
                return is_valid, warnings

            is_valid = check_result.can_start

            if not is_valid:
                warnings.append(f"Execution order violation: {check_result.message}")

        except Exception as e:
            is_valid = False
            warnings.append(f"Error validating execution order: {str(e)}")

        return is_valid, warnings

    def propagate_status_change(self, entity_id: str, entity_type: DependencyType, old_status: str, new_status: str) -> List[StatusBubbleEvent]:
        """
        Propagate status changes up the hierarchy and return all triggered events.
        This provides real-time status propagation functionality.
        """
        events = []

        try:
            # Create initial event
            initial_event = self.create_status_bubble_event(
                entity_id, entity_type, old_status, new_status
            )
            events.append(initial_event)

            # Process automatic progression if status changed to completed
            if new_status == TaskStatus.COMPLETED.value:
                progression_events = self.process_automatic_progression(entity_id, entity_type)
                events.extend(progression_events)

                # Update propagation paths for all events
                for i, event in enumerate(events):
                    event.propagation_path = [e.source_entity_id for e in events[:i+1]]

            # Trigger real-time notifications (placeholder for WebSocket/SSE implementation)
            self._trigger_real_time_notifications(events)

        except Exception as e:
            print(f"Error in status propagation: {str(e)}")

        return events

    def _trigger_real_time_notifications(self, events: List[StatusBubbleEvent]) -> None:
        """
        Trigger real-time notifications for status changes.
        This is a placeholder for WebSocket/SSE implementation.
        """
        # In a real implementation, this would:
        # 1. Send WebSocket messages to connected clients
        # 2. Emit Server-Sent Events
        # 3. Update real-time dashboards
        # 4. Trigger webhooks for external integrations

        for event in events:
            print(f"Real-time notification: {event.source_entity_type.value} {event.source_entity_id} "
                  f"status changed from {event.old_status} to {event.new_status}")

    def get_status_propagation_impact(self, entity_id: str, entity_type: DependencyType, new_status: str) -> Dict[str, Any]:
        """
        Analyze the potential impact of a status change before it happens.
        This helps with real-time status propagation planning.
        """
        impact = {
            "entity_id": entity_id,
            "entity_type": entity_type.value,
            "new_status": new_status,
            "affected_entities": [],
            "automatic_progressions": [],
            "warnings": []
        }

        try:
            # Simulate the status change to see what would happen
            if new_status == TaskStatus.COMPLETED.value:
                if entity_type == DependencyType.TASK:
                    task = self._get_task_with_context(entity_id)
                    if task:
                        # Check if step would be completed
                        step_tasks = self.db.execute(
                            select(Task).where(Task.step_id == task.step_id)
                        ).scalars().all()

                        other_tasks_completed = all(
                            t.status == TaskStatus.COMPLETED or t.id == entity_id
                            for t in step_tasks
                        )

                        if other_tasks_completed:
                            impact["automatic_progressions"].append({
                                "entity_id": task.step.id,
                                "entity_type": "step",
                                "action": "complete"
                            })

                            # Check if phase would be completed
                            phase_steps = self.db.execute(
                                select(Step).where(Step.phase_id == task.step.phase_id)
                            ).scalars().all()

                            other_steps_completed = all(
                                s.status == TaskStatus.COMPLETED or s.id == task.step.id
                                for s in phase_steps
                            )

                            if other_steps_completed:
                                impact["automatic_progressions"].append({
                                    "entity_id": task.step.phase.id,
                                    "entity_type": "phase",
                                    "action": "complete"
                                })

                # Add similar logic for step and phase completion...

        except Exception as e:
            impact["warnings"].append(f"Error analyzing impact: {str(e)}")

        return impact

    def get_real_time_status_summary(self, roadmap_id: str) -> Dict[str, Any]:
        """
        Get a real-time summary of status across the entire roadmap.
        This supports real-time dashboard updates.
        """
        summary = {
            "roadmap_id": roadmap_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "phases": [],
            "overall_progress": 0.0,
            "active_tasks": 0,
            "completed_tasks": 0,
            "blocked_tasks": 0
        }

        try:
            # Get all phases in the roadmap
            phases = self.db.execute(
                select(Phase)
                .options(selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Phase.roadmap_id == roadmap_id)
                .order_by(Phase.order_index)
            ).scalars().all()

            total_tasks = 0
            completed_tasks = 0

            for phase in phases:
                phase_summary = {
                    "id": phase.id,
                    "name": phase.name,
                    "status": phase.status,
                    "steps": [],
                    "progress": 0.0
                }

                phase_tasks = 0
                phase_completed = 0

                for step in phase.steps:
                    step_summary = {
                        "id": step.id,
                        "name": step.name,
                        "status": step.status,
                        "tasks": []
                    }

                    for task in step.tasks:
                        task_summary = {
                            "id": task.id,
                            "name": task.name,
                            "status": task.status,
                            "assigned_agent": task.assigned_agent
                        }
                        step_summary["tasks"].append(task_summary)

                        phase_tasks += 1
                        total_tasks += 1

                        if task.status == TaskStatus.COMPLETED:
                            phase_completed += 1
                            completed_tasks += 1
                        elif task.status == TaskStatus.IN_PROGRESS:
                            summary["active_tasks"] += 1
                        elif task.status == TaskStatus.BLOCKED:
                            summary["blocked_tasks"] += 1

                    phase_summary["steps"].append(step_summary)

                phase_summary["progress"] = (phase_completed / phase_tasks * 100) if phase_tasks > 0 else 0
                summary["phases"].append(phase_summary)

            summary["overall_progress"] = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            summary["completed_tasks"] = completed_tasks

        except Exception as e:
            summary["error"] = f"Error generating summary: {str(e)}"

        return summary

    # Conditional Dependencies Methods

    def check_conditional_dependencies(
        self,
        entity_id: str,
        entity_type: DependencyType,
        context: ConditionEvaluationContext
    ) -> List[ConditionEvaluationResult]:
        """Check conditional dependencies for an entity."""
        results = []

        try:
            # Get conditional dependencies for the entity
            conditional_deps = self._get_conditional_dependencies(entity_id, entity_type)

            for conditional_dep in conditional_deps:
                if conditional_dep.is_active:
                    result = self.condition_evaluator.evaluate_conditional_dependency(
                        conditional_dep, context
                    )
                    results.append(result)

        except Exception as e:
            print(f"Error checking conditional dependencies: {str(e)}")

        return results

    def _get_conditional_dependencies(
        self,
        entity_id: str,
        entity_type: DependencyType
    ) -> List[ConditionalDependency]:
        """Get conditional dependencies for an entity."""
        # In a real implementation, this would query a database table
        # For now, return empty list as conditional dependencies would be stored separately
        return []

    def check_soft_dependencies(self, entity_id: str, entity_type: DependencyType) -> SoftDependencyResult:
        """Check soft dependencies for an entity."""
        try:
            # Get soft dependencies for the entity
            soft_deps = self._get_soft_dependencies(entity_id, entity_type)
            missing_soft_deps = []

            total_quality_impact = 0.0
            total_performance_impact = 0.0
            recommendations = []

            for soft_dep in soft_deps:
                # Check if soft dependency is met
                if not self._is_soft_dependency_met(soft_dep):
                    missing_soft_deps.append(soft_dep)
                    total_quality_impact += soft_dep.quality_impact
                    total_performance_impact += soft_dep.performance_impact

                    if soft_dep.preference_level == "preferred":
                        recommendations.append(
                            f"Consider addressing '{soft_dep.dependency_id}' for better quality"
                        )

            return SoftDependencyResult(
                entity_id=entity_id,
                entity_type=entity_type,
                soft_dependencies=soft_deps,
                missing_soft_dependencies=missing_soft_deps,
                overall_quality_impact=min(total_quality_impact, 1.0),
                overall_performance_impact=min(total_performance_impact, 1.0),
                recommendations=recommendations
            )

        except Exception as e:
            print(f"Error checking soft dependencies: {str(e)}")
            return SoftDependencyResult(
                entity_id=entity_id,
                entity_type=entity_type
            )

    def _get_soft_dependencies(self, entity_id: str, entity_type: DependencyType) -> List[SoftDependency]:
        """Get soft dependencies for an entity."""
        # In a real implementation, this would query a database table
        return []

    def _is_soft_dependency_met(self, soft_dep: SoftDependency) -> bool:
        """Check if a soft dependency is met."""
        # In a real implementation, this would check the actual dependency
        return False

    # Batch Operations Methods

    def batch_check_dependencies(self, request: BatchDependencyCheckRequest) -> BatchDependencyCheckResponse:
        """Check dependencies for multiple entities in a batch."""
        start_time = datetime.now(timezone.utc)
        request_id = str(uuid4())

        results = {}
        soft_dependency_results = {}
        conditional_evaluations = {}
        errors = []

        try:
            for entity_id in request.entity_ids:
                try:
                    # Check regular dependencies
                    if request.entity_type == DependencyType.TASK:
                        check_result = self.can_start_task(entity_id)
                    elif request.entity_type == DependencyType.STEP:
                        check_result = self.can_start_step(entity_id)
                    elif request.entity_type == DependencyType.PHASE:
                        check_result = self.can_start_phase(entity_id)
                    else:
                        raise ValueError(f"Unsupported entity type: {request.entity_type}")

                    results[entity_id] = check_result

                    # Check soft dependencies if requested
                    if request.include_soft_dependencies:
                        soft_result = self.check_soft_dependencies(entity_id, request.entity_type)
                        soft_dependency_results[entity_id] = soft_result

                    # Check conditional dependencies if requested
                    if request.include_conditional_dependencies and request.evaluation_context:
                        conditional_results = self.check_conditional_dependencies(
                            entity_id, request.entity_type, request.evaluation_context
                        )
                        conditional_evaluations[entity_id] = conditional_results

                except Exception as e:
                    errors.append(f"Error checking entity {entity_id}: {str(e)}")

        except Exception as e:
            errors.append(f"Batch processing error: {str(e)}")

        end_time = datetime.now(timezone.utc)
        processing_time = (end_time - start_time).total_seconds()

        return BatchDependencyCheckResponse(
            request_id=request_id,
            results=results,
            soft_dependency_results=soft_dependency_results,
            conditional_evaluations=conditional_evaluations,
            processing_time=processing_time,
            errors=errors
        )

    # External Dependencies Methods

    def check_external_dependency(self, external_dep: ExternalDependency) -> ExternalDependencyStatus:
        """Check the status of an external dependency."""
        try:
            if external_dep.dependency_type == DependencyType.EXTERNAL_API:
                return self._check_api_dependency(external_dep)
            elif external_dep.dependency_type == DependencyType.FILE_DEPENDENCY:
                return self._check_file_dependency(external_dep)
            elif external_dep.dependency_type == DependencyType.ENVIRONMENT:
                return self._check_environment_dependency(external_dep)
            else:
                return ExternalDependencyStatus.UNKNOWN

        except Exception as e:
            print(f"Error checking external dependency {external_dep.dependency_id}: {str(e)}")
            return ExternalDependencyStatus.UNAVAILABLE

    def _check_api_dependency(self, external_dep: ExternalDependency) -> ExternalDependencyStatus:
        """Check API dependency status."""
        try:
            import requests

            if not external_dep.endpoint_url:
                return ExternalDependencyStatus.UNKNOWN

            response = requests.get(
                external_dep.endpoint_url,
                timeout=external_dep.timeout
            )

            if response.status_code == 200:
                return ExternalDependencyStatus.AVAILABLE
            elif 500 <= response.status_code < 600:
                return ExternalDependencyStatus.DEGRADED
            else:
                return ExternalDependencyStatus.UNAVAILABLE

        except Exception as e:
            # Handle requests exceptions if requests is available
            try:
                import requests as req_module
                if isinstance(e, req_module.exceptions.Timeout):
                    return ExternalDependencyStatus.UNAVAILABLE
                elif isinstance(e, req_module.exceptions.ConnectionError):
                    return ExternalDependencyStatus.UNAVAILABLE
            except ImportError:
                pass
            return ExternalDependencyStatus.UNKNOWN

    def _check_file_dependency(self, external_dep: ExternalDependency) -> ExternalDependencyStatus:
        """Check file dependency status."""
        try:
            import os

            if not external_dep.file_path:
                return ExternalDependencyStatus.UNKNOWN

            if os.path.exists(external_dep.file_path):
                return ExternalDependencyStatus.AVAILABLE
            else:
                return ExternalDependencyStatus.UNAVAILABLE

        except Exception:
            return ExternalDependencyStatus.UNKNOWN

    def _check_environment_dependency(self, external_dep: ExternalDependency) -> ExternalDependencyStatus:
        """Check environment variable dependency status."""
        try:
            import os

            if not external_dep.environment_variable:
                return ExternalDependencyStatus.UNKNOWN

            if os.getenv(external_dep.environment_variable):
                return ExternalDependencyStatus.AVAILABLE
            else:
                return ExternalDependencyStatus.UNAVAILABLE

        except Exception:
            return ExternalDependencyStatus.UNKNOWN

    def check_approval_dependency(self, approval_dep: ApprovalDependency) -> bool:
        """Check if approval dependency is satisfied."""
        try:
            if approval_dep.status == "approved":
                return True
            elif approval_dep.status in ["rejected", "expired"]:
                return False

            # Check if auto-approval conditions are met
            if approval_dep.auto_approve_conditions:
                # In a real implementation, this would evaluate the conditions
                pass

            # Check approval requirements
            if approval_dep.approval_type == "any":
                return len(approval_dep.approved_by) > 0
            elif approval_dep.approval_type == "all":
                return len(approval_dep.approved_by) >= len(approval_dep.approvers)
            elif approval_dep.approval_type == "majority":
                required = len(approval_dep.approvers) // 2 + 1
                return len(approval_dep.approved_by) >= required

            return False

        except Exception as e:
            print(f"Error checking approval dependency: {str(e)}")
            return False

    def check_time_dependency(self, time_dep: TimeDependency) -> bool:
        """Check if time dependency is satisfied."""
        try:
            current_time = datetime.now(timezone.utc)

            if time_dep.time_type == "absolute":
                if time_dep.target_time:
                    return current_time >= time_dep.target_time
            elif time_dep.time_type == "relative":
                if time_dep.relative_to_entity and time_dep.relative_offset:
                    # In a real implementation, this would check the related entity's completion time
                    pass
            elif time_dep.time_type == "recurring":
                if time_dep.recurring_pattern:
                    # In a real implementation, this would evaluate the cron pattern
                    pass

            return False

        except Exception as e:
            print(f"Error checking time dependency: {str(e)}")
            return False

    # Cache Management Methods

    def invalidate_dependency_cache(self, entity_id: str):
        """Invalidate cache for a specific entity."""
        self.cache.invalidate_cache(entity_id)

    def clear_all_caches(self):
        """Clear all dependency caches."""
        self.cache.clear_all()

    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        return {
            "dependency_graphs": len(self.cache.dependency_graph_cache),
            "check_results": len(self.cache.check_result_cache),
            "conditions": len(self.cache.condition_cache),
            "external_status": len(self.cache.external_status_cache)
        }

    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive monitoring dashboard data."""
        cache_stats = self.cache.get_cache_stats()
        recent_alerts = self.monitor.get_recent_alerts(hours=24)

        return {
            "cache_performance": cache_stats,
            "recent_alerts": recent_alerts,
            "alert_summary": {
                "total_alerts": len(recent_alerts),
                "critical_alerts": len([a for a in recent_alerts if a.get('severity') == 'critical']),
                "high_alerts": len([a for a in recent_alerts if a.get('severity') == 'high'])
            },
            "performance_metrics": {
                "check_latency": self.monitor.get_metric_summary('check_latency_ms'),
                "cache_hit_rate": self.monitor.get_metric_summary('cache_hit_rate'),
                "failure_rate": self.monitor.get_metric_summary('failure_rate')
            }
        }

    def record_performance_metric(self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record a performance metric."""
        self.monitor.record_metric(metric_name, value, tags)

    def log_dependency_check(self, task_id: str, result: Any, performance_data: Optional[Dict[str, Any]] = None):
        """Safely log dependency check results with complex data."""
        log_data = {
            "task_id": task_id,
            "check_result": result,
            "performance": performance_data or {},
            "timestamp": datetime.now(timezone.utc),
            "cache_stats": self.cache.get_cache_stats()
        }

        # Use safe serialization for logging
        safe_log_data = serialize_dependency_data(log_data)
        logger.info("Dependency check completed", extra={"dependency_data": safe_log_data})

        return safe_log_data

    # AI-Enhanced Features

    async def predict_dependencies_from_description(
        self,
        task_description: str,
        context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Use AI to predict likely dependencies from task descriptions."""
        try:
            orchestrator = self._get_ai_orchestrator()
            if not orchestrator:
                return []

            # Create a prompt for dependency prediction
            prompt = f"""
            Analyze the following task description and predict what dependencies it might have:

            Task: {task_description}

            Context: {json.dumps(context or {}, indent=2)}

            Please identify potential dependencies in the following categories:
            1. Technical dependencies (APIs, databases, services)
            2. File dependencies (configuration files, data files, etc.)
            3. Environment dependencies (environment variables, system requirements)
            4. Task dependencies (other tasks that should be completed first)
            5. Approval dependencies (reviews, approvals needed)

            Return your analysis as a JSON list with the following structure:
            [
                {{
                    "type": "task|external_api|file_dependency|environment|approval",
                    "name": "dependency name",
                    "description": "why this dependency is needed",
                    "confidence": 0.8,
                    "category": "technical|file|environment|task|approval"
                }}
            ]
            """

            # Use the AI orchestrator to analyze the task
            if self._task_request_class:
                task_request = self._task_request_class(
                    task_description=prompt,
                    context={"analysis_type": "dependency_prediction"}
                )
                response = await orchestrator.execute_task(task_request)
            else:
                return []

            # Parse the AI response
            response_content = response.result if hasattr(response, 'result') else str(response)
            if response_content:
                predicted_deps = self._parse_ai_dependency_response(response_content)
                return predicted_deps
            return []

        except Exception as e:
            print(f"Error predicting dependencies: {str(e)}")
            return []

    def _parse_ai_dependency_response(self, ai_response: str) -> List[Dict[str, Any]]:
        """Parse AI response for dependency predictions."""
        try:
            # Try to extract JSON from the response
            import re

            # Look for JSON array in the response
            json_match = re.search(r'\[.*\]', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)

            # If no JSON found, return empty list
            return []

        except Exception as e:
            print(f"Error parsing AI response: {str(e)}")
            return []

    async def suggest_parallel_execution_groups(
        self,
        task_ids: List[str]
    ) -> List[List[str]]:
        """AI-suggested task groupings for parallel execution."""
        try:
            # Get tasks and their dependencies
            task_dependency_map = {}
            task_descriptions = {}

            for task_id in task_ids:
                task = self._get_task_with_context(task_id)
                if task:
                    task_dependency_map[task_id] = task.dependencies
                    task_descriptions[task_id] = task.description or task.name

            # Use AI to analyze parallelization opportunities
            orchestrator = self._get_ai_orchestrator()
            if not orchestrator:
                return []

            prompt = f"""
            Analyze the following tasks and their dependencies to suggest optimal parallel execution groups:

            Tasks and Dependencies:
            {json.dumps(task_dependency_map, indent=2)}

            Task Descriptions:
            {json.dumps(task_descriptions, indent=2)}

            Please suggest groups of tasks that can be executed in parallel, considering:
            1. Dependency constraints
            2. Resource conflicts
            3. Logical groupings
            4. Risk factors

            Return your analysis as a JSON array of arrays:
            [
                ["task1", "task2"],  // Group 1 - can run in parallel
                ["task3"],           // Group 2 - must run after Group 1
                ["task4", "task5"]   // Group 3 - can run in parallel after Group 2
            ]
            """

            if self._task_request_class:
                task_request = self._task_request_class(
                    task_description=prompt,
                    context={"analysis_type": "parallel_execution"}
                )
                response = await orchestrator.execute_task(task_request)
            else:
                return [[task_id] for task_id in task_ids]  # Fallback to sequential

            # Parse the AI response
            response_content = response.result if hasattr(response, 'result') else str(response)
            if response_content:
                parallel_groups = self._parse_ai_parallel_response(response_content)
                return parallel_groups
            return [[task_id] for task_id in task_ids]  # Fallback to sequential

        except Exception as e:
            print(f"Error suggesting parallel execution: {str(e)}")
            return [[task_id] for task_id in task_ids]  # Fallback to sequential

    def _parse_ai_parallel_response(self, ai_response: str) -> List[List[str]]:
        """Parse AI response for parallel execution suggestions."""
        try:
            import re

            # Look for JSON array in the response
            json_match = re.search(r'\[.*\]', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)

            return []

        except Exception as e:
            print(f"Error parsing AI parallel response: {str(e)}")
            return []

    async def estimate_completion_time(
        self,
        entity_id: str,
        entity_type: DependencyType
    ) -> Optional[timedelta]:
        """AI-based completion time estimation considering dependencies."""
        try:
            # Get entity details and dependencies
            if entity_type == DependencyType.TASK:
                entity = self._get_task_with_context(entity_id)
            elif entity_type == DependencyType.STEP:
                entity = self._get_step_with_context(entity_id)
            elif entity_type == DependencyType.PHASE:
                entity = self._get_phase_with_context(entity_id)
            else:
                return None

            if not entity:
                return None

            # Get dependency information
            dependency_check = self.can_start_task(entity_id) if entity_type == DependencyType.TASK else None

            # Use AI to estimate completion time
            orchestrator = self._get_ai_orchestrator()
            if not orchestrator:
                return timedelta(hours=1.0)  # Default estimate

            prompt = f"""
            Estimate the completion time for the following {entity_type.value}:

            Name: {entity.name}
            Description: {getattr(entity, 'description', 'No description')}
            Dependencies: {getattr(entity, 'dependencies', [])}
            Current Status: {getattr(entity, 'status', 'unknown')}

            Dependency Analysis: {dependency_check.message if dependency_check else 'No analysis available'}

            Please provide an estimated completion time in hours, considering:
            1. Task complexity
            2. Dependency wait times
            3. Resource availability
            4. Historical data patterns
            5. Risk factors

            Return just a number representing hours (e.g., 4.5 for 4.5 hours).
            """

            if self._task_request_class:
                task_request = self._task_request_class(
                    task_description=prompt,
                    context={"analysis_type": "time_estimation"}
                )
                response = await orchestrator.execute_task(task_request)
            else:
                return timedelta(hours=1.0)  # Default estimate

            # Parse the AI response
            response_content = response.result if hasattr(response, 'result') else str(response)
            if response_content:
                hours = self._parse_ai_time_response(response_content)
                if hours:
                    return timedelta(hours=hours)

            return None

        except Exception as e:
            print(f"Error estimating completion time: {str(e)}")
            return None

    def _parse_ai_time_response(self, ai_response: str) -> Optional[float]:
        """Parse AI response for time estimation."""
        try:
            import re

            # Look for numbers in the response
            number_match = re.search(r'(\d+\.?\d*)', ai_response)
            if number_match:
                return float(number_match.group(1))

            return None

        except Exception as e:
            print(f"Error parsing AI time response: {str(e)}")
            return None

    # Analytics Methods

    def analyze_bottlenecks(self, roadmap_id: str) -> List[DependencyBottleneck]:
        """Identify dependency bottlenecks in a roadmap."""
        bottlenecks = []

        try:
            # Get all entities in the roadmap
            phases = self.db.execute(
                select(Phase)
                .options(selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Phase.roadmap_id == roadmap_id)
            ).scalars().all()

            # Analyze each entity for bottlenecks
            for phase in phases:
                # Check phase-level bottlenecks
                phase_bottleneck = self._analyze_entity_bottleneck(
                    phase.id, DependencyType.PHASE, phase.name
                )
                if phase_bottleneck:
                    bottlenecks.append(phase_bottleneck)

                for step in phase.steps:
                    # Check step-level bottlenecks
                    step_bottleneck = self._analyze_entity_bottleneck(
                        step.id, DependencyType.STEP, step.name
                    )
                    if step_bottleneck:
                        bottlenecks.append(step_bottleneck)

                    for task in step.tasks:
                        # Check task-level bottlenecks
                        task_bottleneck = self._analyze_entity_bottleneck(
                            task.id, DependencyType.TASK, task.name
                        )
                        if task_bottleneck:
                            bottlenecks.append(task_bottleneck)

            # Sort by severity
            severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
            bottlenecks.sort(key=lambda x: severity_order.get(x.severity, 4))

        except Exception as e:
            print(f"Error analyzing bottlenecks: {str(e)}")

        return bottlenecks

    def _analyze_entity_bottleneck(
        self,
        entity_id: str,
        entity_type: DependencyType,
        entity_name: str
    ) -> Optional[DependencyBottleneck]:
        """Analyze a single entity for bottlenecks."""
        try:
            # Get dependency check result
            if entity_type == DependencyType.TASK:
                check_result = self.can_start_task(entity_id)
            elif entity_type == DependencyType.STEP:
                check_result = self.can_start_step(entity_id)
            elif entity_type == DependencyType.PHASE:
                check_result = self.can_start_phase(entity_id)
            else:
                return None

            # Determine if this is a bottleneck
            if not check_result.can_start and len(check_result.blocking_dependencies) > 0:
                # Count affected entities (entities that depend on this one)
                affected_entities = self._get_dependent_entities(entity_id, entity_type)

                # Determine severity based on number of affected entities and blocking dependencies
                if len(affected_entities) > 10 or len(check_result.blocking_dependencies) > 5:
                    severity = "critical"
                elif len(affected_entities) > 5 or len(check_result.blocking_dependencies) > 3:
                    severity = "high"
                elif len(affected_entities) > 2 or len(check_result.blocking_dependencies) > 1:
                    severity = "medium"
                else:
                    severity = "low"

                # Determine bottleneck type
                bottleneck_type = "blocking"
                for dep in check_result.blocking_dependencies:
                    if dep.dependency_type == DependencyType.EXTERNAL_API:
                        bottleneck_type = "external"
                        break
                    elif dep.dependency_type == DependencyType.APPROVAL:
                        bottleneck_type = "approval"
                        break

                # Generate resolution suggestions
                suggestions = self._generate_bottleneck_suggestions(check_result)

                return DependencyBottleneck(
                    entity_id=entity_id,
                    entity_type=entity_type,
                    entity_name=entity_name,
                    bottleneck_type=bottleneck_type,
                    severity=severity,
                    affected_entities=affected_entities,
                    estimated_delay=len(check_result.blocking_dependencies) * 2,  # Simple estimation
                    resolution_suggestions=suggestions
                )

            return None

        except Exception as e:
            print(f"Error analyzing entity bottleneck: {str(e)}")
            return None

    def _get_dependent_entities(self, entity_id: str, entity_type: DependencyType) -> List[str]:
        """Get entities that depend on the given entity."""
        # In a real implementation, this would query the database for reverse dependencies
        # For now, return empty list
        return []

    def _generate_bottleneck_suggestions(self, check_result: DependencyCheckResult) -> List[str]:
        """Generate suggestions to resolve bottlenecks."""
        suggestions = []

        for dep in check_result.blocking_dependencies:
            if dep.dependency_type == DependencyType.TASK:
                suggestions.append(f"Prioritize completion of task '{dep.dependency_name}'")
            elif dep.dependency_type == DependencyType.EXTERNAL_API:
                suggestions.append(f"Check external API '{dep.dependency_name}' availability")
            elif dep.dependency_type == DependencyType.APPROVAL:
                suggestions.append(f"Follow up on approval for '{dep.dependency_name}'")
            elif dep.dependency_type == DependencyType.ENVIRONMENT:
                suggestions.append(f"Set up environment dependency '{dep.dependency_name}'")

        if check_result.override_level != OverrideLevel.NONE:
            suggestions.append(f"Consider using {check_result.override_level.value} override if appropriate")

        return suggestions

    def generate_dependency_metrics(
        self,
        roadmap_id: str,
        timeframe_start: datetime,
        timeframe_end: datetime
    ) -> DependencyMetrics:
        """Generate dependency metrics for a timeframe."""
        try:
            # In a real implementation, this would query historical data
            # For now, generate sample metrics

            return DependencyMetrics(
                roadmap_id=roadmap_id,
                timeframe_start=timeframe_start,
                timeframe_end=timeframe_end,
                total_dependencies_checked=150,
                dependencies_resolved=120,
                dependencies_blocked=30,
                average_resolution_time=4.5,
                total_overrides=5,
                override_success_rate=0.8,
                bottlenecks_detected=3,
                critical_bottlenecks=1,
                average_check_time=25.0,
                cache_hit_rate=0.75,
                ai_prediction_accuracy=0.85
            )

        except Exception as e:
            print(f"Error generating metrics: {str(e)}")
            return DependencyMetrics(
                roadmap_id=roadmap_id,
                timeframe_start=timeframe_start,
                timeframe_end=timeframe_end
            )

    async def predict_project_delays(self, roadmap_id: str) -> List[DelayPrediction]:
        """Predict potential delays in project timeline."""
        predictions = []

        try:
            # Get current bottlenecks
            bottlenecks = self.analyze_bottlenecks(roadmap_id)

            # Use AI to predict delays based on bottlenecks
            orchestrator = self._get_ai_orchestrator()
            if not orchestrator:
                return []

            for bottleneck in bottlenecks:
                prompt = f"""
                Analyze the following dependency bottleneck and predict potential delays:

                Entity: {bottleneck.entity_name} ({bottleneck.entity_type.value})
                Severity: {bottleneck.severity}
                Type: {bottleneck.bottleneck_type}
                Affected Entities: {len(bottleneck.affected_entities)}
                Current Estimated Delay: {bottleneck.estimated_delay} hours

                Please predict:
                1. Realistic delay in hours
                2. Confidence level (0-1)
                3. Main causes of delay
                4. Mitigation strategies
                5. Impact on overall project

                Return as JSON:
                {{
                    "delay_hours": 8,
                    "confidence": 0.7,
                    "causes": ["dependency blocking", "resource unavailable"],
                    "mitigations": ["prioritize dependency", "allocate more resources"],
                    "impact": "Minor delay to overall timeline"
                }}
                """

                if self._task_request_class:
                    task_request = self._task_request_class(
                        task_description=prompt,
                        context={"analysis_type": "delay_prediction"}
                    )
                    response = await orchestrator.execute_task(task_request)
                else:
                    continue

                # Parse AI response
                response_content = response.result if hasattr(response, 'result') else str(response)
                prediction_data = self._parse_ai_delay_response(response_content) if response_content else None
                if prediction_data:
                    prediction = DelayPrediction(
                        entity_id=bottleneck.entity_id,
                        entity_type=bottleneck.entity_type,
                        entity_name=bottleneck.entity_name,
                        predicted_delay=prediction_data.get("delay_hours", bottleneck.estimated_delay or 0),
                        confidence=prediction_data.get("confidence", 0.5),
                        delay_causes=prediction_data.get("causes", []),
                        mitigation_strategies=prediction_data.get("mitigations", []),
                        impact_on_project=prediction_data.get("impact", "Unknown impact")
                    )
                    predictions.append(prediction)

        except Exception as e:
            print(f"Error predicting delays: {str(e)}")

        return predictions

    def _parse_ai_delay_response(self, ai_response: str) -> Optional[Dict[str, Any]]:
        """Parse AI response for delay predictions."""
        try:
            import re

            # Look for JSON in the response
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)

            return None

        except Exception as e:
            print(f"Error parsing AI delay response: {str(e)}")
            return None

    # Visualization Methods

    def generate_dependency_graph_visualization(
        self,
        roadmap_id: str,
        layout: str = "hierarchical",
        filters: Optional[Dict[str, Any]] = None
    ) -> DependencyGraphVisualization:
        """Generate dependency graph visualization data."""
        try:
            nodes = []
            edges = []

            # Get all entities in the roadmap
            phases = self.db.execute(
                select(Phase)
                .options(selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Phase.roadmap_id == roadmap_id)
            ).scalars().all()

            # Create nodes for each entity
            y_offset = 0
            for phase in phases:
                # Add phase node
                phase_node = VisualizationNode(
                    id=phase.id,
                    label=phase.name,
                    type=DependencyType.PHASE,
                    status=phase.status,
                    x=0,
                    y=y_offset,
                    color=self._get_status_color(phase.status),
                    size=30,
                    metadata={"description": phase.description or ""}
                )
                nodes.append(phase_node)

                x_offset = 100
                for step in phase.steps:
                    # Add step node
                    step_node = VisualizationNode(
                        id=step.id,
                        label=step.name,
                        type=DependencyType.STEP,
                        status=step.status,
                        x=x_offset,
                        y=y_offset,
                        color=self._get_status_color(step.status),
                        size=20,
                        metadata={"description": step.description or ""}
                    )
                    nodes.append(step_node)

                    # Add edge from phase to step
                    edges.append(VisualizationEdge(
                        source=phase.id,
                        target=step.id,
                        type="dependency",
                        weight=1.0,
                        color="#666666",
                        style="solid"
                    ))

                    task_y_offset = y_offset + 50
                    for task in step.tasks:
                        # Add task node
                        task_node = VisualizationNode(
                            id=task.id,
                            label=task.name,
                            type=DependencyType.TASK,
                            status=task.status,
                            x=x_offset,
                            y=task_y_offset,
                            color=self._get_status_color(task.status),
                            size=15,
                            metadata={
                                "description": task.description or "",
                                "assigned_agent": task.assigned_agent
                            }
                        )
                        nodes.append(task_node)

                        # Add edge from step to task
                        edges.append(VisualizationEdge(
                            source=step.id,
                            target=task.id,
                            type="dependency",
                            weight=1.0,
                            color="#666666",
                            style="solid"
                        ))

                        # Add dependency edges
                        for dep_id in task.dependencies:
                            edges.append(VisualizationEdge(
                                source=dep_id,
                                target=task.id,
                                type="blocking",
                                weight=2.0,
                                color="#ff6b6b",
                                style="dashed"
                            ))

                        task_y_offset += 30

                    x_offset += 150

                y_offset += 200

            # Ensure layout is a valid literal
            valid_layouts = ["hierarchical", "force", "circular", "grid"]
            layout_value = layout if layout in valid_layouts else "hierarchical"

            return DependencyGraphVisualization(
                roadmap_id=roadmap_id,
                title=f"Dependency Graph - Roadmap {roadmap_id}",
                nodes=nodes,
                edges=edges,
                layout=cast(Literal["hierarchical", "force", "circular", "grid"], layout_value),
                filters=filters or {}
            )

        except Exception as e:
            print(f"Error generating dependency graph: {str(e)}")
            return DependencyGraphVisualization(
                roadmap_id=roadmap_id,
                title="Error generating graph",
                nodes=[],
                edges=[]
            )

    def _get_status_color(self, status: str) -> str:
        """Get color for status visualization."""
        color_map = {
            "pending": "#ffd93d",
            "in_progress": "#6bcf7f",
            "completed": "#4dabf7",
            "failed": "#ff6b6b",
            "blocked": "#fd7e14"
        }
        return color_map.get(status.lower(), "#868e96")

    def generate_gantt_chart(
        self,
        roadmap_id: str,
        time_scale: str = "days",
        show_dependencies: bool = True
    ) -> GanttChart:
        """Generate Gantt chart visualization data."""
        try:
            tasks = []
            start_date = datetime.now(timezone.utc)
            end_date = start_date

            # Get all entities in the roadmap
            phases = self.db.execute(
                select(Phase)
                .options(selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Phase.roadmap_id == roadmap_id)
            ).scalars().all()

            current_date = start_date

            for phase in phases:
                phase_start = current_date
                phase_end = current_date

                for step in phase.steps:
                    step_start = current_date
                    step_end = current_date

                    for task in step.tasks:
                        # Estimate task duration (in a real system, this would be stored)
                        estimated_hours = self._estimate_task_duration(task)
                        task_end = current_date + timedelta(hours=estimated_hours)

                        gantt_task = GanttChartTask(
                            id=task.id,
                            name=task.name,
                            start_date=current_date,
                            end_date=task_end,
                            duration=estimated_hours,
                            progress=self._calculate_task_progress(task),
                            dependencies=task.dependencies,
                            assigned_to=str(task.assigned_agent),
                            priority=cast(Literal["low", "medium", "high", "critical"], self._determine_task_priority(task)),
                            status=str(task.status),
                            color=self._get_status_color(str(task.status)),
                            parent_id=step.id
                        )
                        tasks.append(gantt_task)

                        current_date = task_end
                        step_end = max(step_end, task_end)

                    phase_end = max(phase_end, step_end)

                end_date = max(end_date, phase_end)

            return GanttChart(
                roadmap_id=roadmap_id,
                title=f"Project Timeline - Roadmap {roadmap_id}",
                tasks=tasks,
                start_date=start_date,
                end_date=end_date,
                time_scale=cast(Literal["hours", "days", "weeks", "months"], time_scale),
                show_dependencies=show_dependencies,
                show_critical_path=True
            )

        except Exception as e:
            print(f"Error generating Gantt chart: {str(e)}")
            return GanttChart(
                roadmap_id=roadmap_id,
                title="Error generating chart",
                tasks=[],
                start_date=datetime.now(timezone.utc),
                end_date=datetime.now(timezone.utc)
            )

    def _estimate_task_duration(self, task: Task) -> int:
        """Estimate task duration in hours."""
        # Simple estimation based on task complexity
        base_hours = 4
        if "complex" in (task.description or "").lower():
            return base_hours * 2
        elif "simple" in (task.description or "").lower():
            return base_hours // 2
        return base_hours

    def _calculate_task_progress(self, task: Task) -> float:
        """Calculate task progress percentage."""
        if task.status == TaskStatus.COMPLETED:
            return 1.0
        elif task.status == TaskStatus.IN_PROGRESS:
            return 0.5
        else:
            return 0.0

    def _determine_task_priority(self, task: Task) -> str:
        """Determine task priority."""
        # Simple priority determination
        if "critical" in (task.description or "").lower():
            return "critical"
        elif "high" in (task.description or "").lower():
            return "high"
        elif "low" in (task.description or "").lower():
            return "low"
        return "medium"

    def create_dependency_dashboard(
        self,
        roadmap_id: str,
        user_id: str,
        dashboard_name: str
    ) -> DependencyDashboard:
        """Create a dependency management dashboard."""
        try:
            dashboard_id = str(uuid4())

            # Create default widgets
            widgets = [
                DashboardWidget(
                    id=str(uuid4()),
                    type="metric",
                    title="Dependency Overview",
                    data={
                        "total_dependencies": 25,
                        "resolved_dependencies": 18,
                        "blocked_dependencies": 7,
                        "resolution_rate": 0.72
                    },
                    position={"x": 0, "y": 0, "width": 6, "height": 3},
                    refresh_interval=30
                ),
                DashboardWidget(
                    id=str(uuid4()),
                    type="chart",
                    title="Dependency Resolution Trend",
                    data={
                        "chart_type": "line",
                        "data_points": [
                            {"date": "2024-01-01", "resolved": 5, "blocked": 3},
                            {"date": "2024-01-02", "resolved": 8, "blocked": 2},
                            {"date": "2024-01-03", "resolved": 12, "blocked": 4}
                        ]
                    },
                    position={"x": 6, "y": 0, "width": 6, "height": 4},
                    refresh_interval=60
                ),
                DashboardWidget(
                    id=str(uuid4()),
                    type="list",
                    title="Critical Bottlenecks",
                    data={
                        "items": [
                            {"name": "API Integration", "severity": "critical", "affected": 5},
                            {"name": "Database Setup", "severity": "high", "affected": 3}
                        ]
                    },
                    position={"x": 0, "y": 3, "width": 12, "height": 3},
                    refresh_interval=120
                )
            ]

            return DependencyDashboard(
                id=dashboard_id,
                name=dashboard_name,
                roadmap_id=roadmap_id,
                user_id=user_id,
                widgets=widgets
            )

        except Exception as e:
            print(f"Error creating dashboard: {str(e)}")
            return DependencyDashboard(
                id=str(uuid4()),
                name=dashboard_name,
                roadmap_id=roadmap_id,
                user_id=user_id,
                widgets=[]
            )

    # External Tool Integration Methods

    def create_jira_integration(
        self,
        roadmap_id: str,
        jira_config: ExternalToolConfig,
        user_id: str
    ) -> ExternalToolIntegration:
        """Create JIRA integration for dependency synchronization."""
        try:
            integration_id = str(uuid4())

            integration = ExternalToolIntegration(
                id=integration_id,
                roadmap_id=roadmap_id,
                tool_config=jira_config,
                created_by=user_id,
                last_sync_result=None,
                sync_schedule=None
            )

            return integration

        except Exception as e:
            print(f"Error creating JIRA integration: {str(e)}")
            raise

    def sync_with_jira(self, integration: ExternalToolIntegration) -> ExternalToolSyncResult:
        """Synchronize dependencies with JIRA."""
        try:
            sync_result = ExternalToolSyncResult(
                tool_config_id=integration.id,
                sync_type="bidirectional",
                status=ExternalToolSyncStatus.IN_PROGRESS,
                completed_at=None,
                next_sync_at=None
            )

            # In a real implementation, this would:
            # 1. Connect to JIRA API
            # 2. Fetch issues based on filters
            # 3. Map JIRA issues to internal tasks
            # 4. Update dependencies based on JIRA issue links
            # 5. Push internal dependency changes back to JIRA

            # Simulate sync process
            sync_result.status = ExternalToolSyncStatus.SUCCESS
            sync_result.items_processed = 15
            sync_result.items_updated = 8
            sync_result.completed_at = datetime.now(timezone.utc)

            return sync_result

        except Exception as e:
            print(f"Error syncing with JIRA: {str(e)}")
            return ExternalToolSyncResult(
                tool_config_id=integration.id,
                sync_type="bidirectional",
                status=ExternalToolSyncStatus.FAILED,
                errors=[str(e)],
                completed_at=datetime.now(timezone.utc),
                next_sync_at=None
            )

    def create_github_integration(
        self,
        roadmap_id: str,
        github_config: ExternalToolConfig,
        user_id: str
    ) -> ExternalToolIntegration:
        """Create GitHub integration for dependency synchronization."""
        try:
            integration_id = str(uuid4())

            integration = ExternalToolIntegration(
                id=integration_id,
                roadmap_id=roadmap_id,
                tool_config=github_config,
                created_by=user_id,
                last_sync_result=None,
                sync_schedule=None
            )

            return integration

        except Exception as e:
            print(f"Error creating GitHub integration: {str(e)}")
            raise

    def sync_with_github(self, integration: ExternalToolIntegration) -> ExternalToolSyncResult:
        """Synchronize dependencies with GitHub."""
        try:
            sync_result = ExternalToolSyncResult(
                tool_config_id=integration.id,
                sync_type="bidirectional",
                status=ExternalToolSyncStatus.IN_PROGRESS,
                completed_at=None,
                next_sync_at=None
            )

            # In a real implementation, this would:
            # 1. Connect to GitHub API
            # 2. Fetch issues and PRs based on filters
            # 3. Map GitHub issues to internal tasks
            # 4. Update dependencies based on GitHub issue dependencies
            # 5. Create/update GitHub issues for internal tasks

            # Simulate sync process
            sync_result.status = ExternalToolSyncStatus.SUCCESS
            sync_result.items_processed = 12
            sync_result.items_created = 3
            sync_result.items_updated = 5
            sync_result.completed_at = datetime.now(timezone.utc)

            return sync_result

        except Exception as e:
            print(f"Error syncing with GitHub: {str(e)}")
            return ExternalToolSyncResult(
                tool_config_id=integration.id,
                sync_type="bidirectional",
                status=ExternalToolSyncStatus.FAILED,
                errors=[str(e)],
                completed_at=datetime.now(timezone.utc),
                next_sync_at=None
            )

    def create_cicd_integration(
        self,
        roadmap_id: str,
        cicd_config: ExternalToolConfig,
        user_id: str
    ) -> ExternalToolIntegration:
        """Create CI/CD integration for dependency automation."""
        try:
            integration_id = str(uuid4())

            integration = ExternalToolIntegration(
                id=integration_id,
                roadmap_id=roadmap_id,
                tool_config=cicd_config,
                created_by=user_id,
                last_sync_result=None,
                sync_schedule=None
            )

            return integration

        except Exception as e:
            print(f"Error creating CI/CD integration: {str(e)}")
            raise

    def trigger_cicd_pipeline(
        self,
        integration: ExternalToolIntegration,
        task_id: str,
        pipeline_mapping: CICDPipelineMapping
    ) -> bool:
        """Trigger CI/CD pipeline based on task status change."""
        try:
            # In a real implementation, this would:
            # 1. Check if task status matches trigger conditions
            # 2. Call CI/CD API to trigger pipeline
            # 3. Update pipeline mapping with trigger information

            # Use integration and task_id for logging
            print(f"Triggering pipeline for task {task_id} using integration {integration.id}")

            # Simulate pipeline trigger
            pipeline_mapping.last_triggered = datetime.now(timezone.utc)
            pipeline_mapping.last_status = "triggered"

            return True

        except Exception as e:
            print(f"Error triggering CI/CD pipeline: {str(e)}")
            return False

    def get_integration_status(self, integration_id: str) -> Dict[str, Any]:
        """Get status of external tool integration."""
        try:
            # In a real implementation, this would query the database
            return {
                "integration_id": integration_id,
                "status": "active",
                "last_sync": datetime.now(timezone.utc).isoformat(),
                "sync_health": "good",
                "error_count": 0,
                "next_sync": (datetime.now(timezone.utc) + timedelta(minutes=5)).isoformat()
            }

        except Exception as e:
            print(f"Error getting integration status: {str(e)}")
            return {
                "integration_id": integration_id,
                "status": "error",
                "error": str(e)
            }
