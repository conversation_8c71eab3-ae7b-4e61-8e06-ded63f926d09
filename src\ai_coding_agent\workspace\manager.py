"""
Workspace management for project lifecycle and organization.

This module provides high-level workspace management functionality
including project creation, archiving, and lifecycle management.
"""

import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from .structure import WorkspaceStructure, create_workspace_structure
from ..platform import set_platform_permissions

logger = logging.getLogger(__name__)


class WorkspaceManager:
    """High-level workspace management for AI Coding Agent projects."""

    def __init__(self):
        self.structure: Optional[WorkspaceStructure] = None
        self._initialize_workspace()

    def _initialize_workspace(self) -> None:
        """Initialize the workspace structure."""
        try:
            self.structure = create_workspace_structure()
            logger.info("Workspace manager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize workspace: {e}")
            raise RuntimeError(f"Workspace initialization failed: {e}")

    def create_project(self, project_name: str, description: str = "") -> Path:
        """
        Create a new project in the active workspace.

        Args:
            project_name: Name of the project (must be valid directory name)
            description: Optional project description

        Returns:
            Path: Path to the created project directory

        Raises:
            ValueError: If project name is invalid
            RuntimeError: If project creation fails
        """
        try:
            # Validate project name
            if not self._is_valid_project_name(project_name):
                raise ValueError(f"Invalid project name: {project_name}")

            # Check if project already exists
            project_path = self.structure.active_dir / project_name
            if project_path.exists():
                raise ValueError(f"Project already exists: {project_name}")

            # Create project directory
            project_path.mkdir(parents=True, exist_ok=False)

            # Set proper permissions
            set_platform_permissions(project_path, owner_only=True)

            # Create project metadata
            self._create_project_metadata(project_path, project_name, description)

            logger.info(f"Created project: {project_name} at {project_path}")
            return project_path

        except Exception as e:
            logger.error(f"Failed to create project {project_name}: {e}")
            raise RuntimeError(f"Project creation failed: {e}")

    def archive_project(self, project_name: str) -> Path:
        """
        Archive an active project by moving it to the archived directory.

        Args:
            project_name: Name of the project to archive

        Returns:
            Path: Path to the archived project directory

        Raises:
            ValueError: If project doesn't exist
            RuntimeError: If archiving fails
        """
        try:
            active_path = self.structure.active_dir / project_name
            if not active_path.exists():
                raise ValueError(f"Project not found: {project_name}")

            # Create archived path with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archived_name = f"{project_name}_{timestamp}"
            archived_path = self.structure.archived_dir / archived_name

            # Move project to archived directory
            shutil.move(str(active_path), str(archived_path))

            # Update project metadata
            self._update_project_metadata(archived_path, {"archived_at": datetime.now().isoformat()})

            logger.info(f"Archived project: {project_name} to {archived_path}")
            return archived_path

        except Exception as e:
            logger.error(f"Failed to archive project {project_name}: {e}")
            raise RuntimeError(f"Project archiving failed: {e}")

    def restore_project(self, archived_project_name: str, new_name: Optional[str] = None) -> Path:
        """
        Restore an archived project back to active status.

        Args:
            archived_project_name: Name of the archived project
            new_name: Optional new name for the restored project

        Returns:
            Path: Path to the restored project directory

        Raises:
            ValueError: If archived project doesn't exist
            RuntimeError: If restoration fails
        """
        try:
            archived_path = self.structure.archived_dir / archived_project_name
            if not archived_path.exists():
                raise ValueError(f"Archived project not found: {archived_project_name}")

            # Determine restored project name
            if new_name:
                if not self._is_valid_project_name(new_name):
                    raise ValueError(f"Invalid new project name: {new_name}")
                restored_name = new_name
            else:
                # Extract original name (remove timestamp suffix)
                restored_name = archived_project_name.split('_')[0]

            # Check if active project with same name exists
            active_path = self.structure.active_dir / restored_name
            if active_path.exists():
                raise ValueError(f"Active project with name '{restored_name}' already exists")

            # Move project back to active directory
            shutil.move(str(archived_path), str(active_path))

            # Update project metadata
            self._update_project_metadata(active_path, {
                "restored_at": datetime.now().isoformat(),
                "restored_from": archived_project_name
            })

            logger.info(f"Restored project: {archived_project_name} to {active_path}")
            return active_path

        except Exception as e:
            logger.error(f"Failed to restore project {archived_project_name}: {e}")
            raise RuntimeError(f"Project restoration failed: {e}")

    def list_active_projects(self) -> List[str]:
        """
        List all active projects.

        Returns:
            List[str]: List of active project names
        """
        try:
            if not self.structure.active_dir.exists():
                return []

            projects = [
                item.name for item in self.structure.active_dir.iterdir()
                if item.is_dir() and not item.name.startswith('.')
            ]

            logger.debug(f"Found {len(projects)} active projects")
            return sorted(projects)

        except Exception as e:
            logger.error(f"Failed to list active projects: {e}")
            return []

    def list_archived_projects(self) -> List[str]:
        """
        List all archived projects.

        Returns:
            List[str]: List of archived project names
        """
        try:
            if not self.structure.archived_dir.exists():
                return []

            projects = [
                item.name for item in self.structure.archived_dir.iterdir()
                if item.is_dir() and not item.name.startswith('.')
            ]

            logger.debug(f"Found {len(projects)} archived projects")
            return sorted(projects)

        except Exception as e:
            logger.error(f"Failed to list archived projects: {e}")
            return []

    def get_project_path(self, project_name: str, active: bool = True) -> Optional[Path]:
        """
        Get the path to a specific project.

        Args:
            project_name: Name of the project
            active: Whether to look in active (True) or archived (False) projects

        Returns:
            Optional[Path]: Path to the project if it exists, None otherwise
        """
        try:
            if active:
                project_path = self.structure.active_dir / project_name
            else:
                project_path = self.structure.archived_dir / project_name

            return project_path if project_path.exists() else None

        except Exception as e:
            logger.error(f"Failed to get project path for {project_name}: {e}")
            return None

    def cleanup_temp_directory(self) -> bool:
        """
        Clean up the temporary directory by removing old files.

        Returns:
            bool: True if cleanup was successful
        """
        try:
            if not self.structure.temp_dir.exists():
                return True

            # Remove all files and subdirectories in temp
            for item in self.structure.temp_dir.iterdir():
                if item.is_file():
                    item.unlink()
                elif item.is_dir():
                    shutil.rmtree(item)

            logger.info("Temporary directory cleaned up successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to cleanup temp directory: {e}")
            return False

    def _is_valid_project_name(self, name: str) -> bool:
        """Validate project name for filesystem compatibility."""
        if not name or len(name) > 255:
            return False

        # Check for invalid characters
        invalid_chars = '<>:"/\\|?*'
        if any(char in name for char in invalid_chars):
            return False

        # Check for reserved names (Windows)
        reserved_names = {'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4',
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2',
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'}
        if name.upper() in reserved_names:
            return False

        return True

    def _create_project_metadata(self, project_path: Path, name: str, description: str) -> None:
        """Create metadata file for a project."""
        try:
            import json

            metadata = {
                "name": name,
                "description": description,
                "created_at": datetime.now().isoformat(),
                "version": "1.0",
                "status": "active"
            }

            metadata_file = project_path / ".project_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)

            logger.debug(f"Created project metadata: {metadata_file}")

        except Exception as e:
            logger.warning(f"Failed to create project metadata: {e}")

    def _update_project_metadata(self, project_path: Path, updates: Dict[str, Any]) -> None:
        """Update project metadata with new information."""
        try:
            import json

            metadata_file = project_path / ".project_metadata.json"

            # Load existing metadata
            metadata = {}
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)

            # Apply updates
            metadata.update(updates)
            metadata["updated_at"] = datetime.now().isoformat()

            # Save updated metadata
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)

            logger.debug(f"Updated project metadata: {metadata_file}")

        except Exception as e:
            logger.warning(f"Failed to update project metadata: {e}")


# Global workspace manager instance
_workspace_manager: Optional[WorkspaceManager] = None


def get_workspace_manager() -> WorkspaceManager:
    """
    Get the global workspace manager instance.

    Returns:
        WorkspaceManager: Global workspace manager
    """
    global _workspace_manager
    if _workspace_manager is None:
        _workspace_manager = WorkspaceManager()
    return _workspace_manager
