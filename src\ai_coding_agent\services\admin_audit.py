"""
Admin audit logging service for tracking administrative actions.

This module provides comprehensive audit logging for all admin operations,
ensuring accountability and forensic capabilities.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from enum import Enum

from ..models import User


class AuditAction(str, Enum):
    """Types of admin actions that can be audited."""
    MODEL_CONFIG_UPDATE = "model_config_update"
    PROVIDER_CONFIG_ADD = "provider_config_add"
    PROVIDER_CONFIG_UPDATE = "provider_config_update"
    PROVIDER_CONFIG_DELETE = "provider_config_delete"
    SYSTEM_HEALTH_CHECK = "system_health_check"
    MODEL_CONNECTION_TEST = "model_connection_test"
    USER_PERMISSION_CHANGE = "user_permission_change"
    ADMIN_LOGIN = "admin_login"
    ADMIN_LOGOUT = "admin_logout"
    CONFIG_EXPORT = "config_export"
    CONFIG_IMPORT = "config_import"


class AuditSeverity(str, Enum):
    """Severity levels for audit events."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AdminAuditLogger:
    """
    Comprehensive audit logging for admin operations.
    
    Features:
    - Structured JSON logging
    - Multiple output formats (file, database, external systems)
    - Configurable retention policies
    - Security event correlation
    """
    
    def __init__(self):
        """Initialize the audit logger."""
        self.log_dir = Path(__file__).parent.parent / "logs"
        self.log_dir.mkdir(exist_ok=True)
        
        # Setup file logger
        self.logger = logging.getLogger("admin_audit")
        self.logger.setLevel(logging.INFO)
        
        # Create file handler with rotation
        log_file = self.log_dir / "admin_audit.log"
        handler = logging.FileHandler(log_file)
        handler.setLevel(logging.INFO)
        
        # JSON formatter for structured logging
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        if not self.logger.handlers:
            self.logger.addHandler(handler)
    
    def log_admin_action(
        self,
        action: AuditAction,
        user: User,
        details: Dict[str, Any],
        severity: AuditSeverity = AuditSeverity.INFO,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """
        Log an administrative action.
        
        Args:
            action: The type of action performed
            user: The admin user who performed the action
            details: Additional details about the action
            severity: Severity level of the action
            ip_address: IP address of the user
            user_agent: User agent string
        """
        audit_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "action": action.value,
            "severity": severity.value,
            "user": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_superuser": getattr(user, 'is_superuser', False)
            },
            "details": details,
            "metadata": {
                "ip_address": ip_address,
                "user_agent": user_agent,
                "session_id": getattr(user, 'session_id', None)
            }
        }
        
        # Log as JSON
        self.logger.info(json.dumps(audit_entry))
        
        # For critical actions, also log to console
        if severity == AuditSeverity.CRITICAL:
            print(f"CRITICAL ADMIN ACTION: {action.value} by {user.username}")
    
    def log_model_config_change(
        self,
        user: User,
        old_config: Dict[str, Any],
        new_config: Dict[str, Any],
        ip_address: Optional[str] = None
    ) -> None:
        """Log model configuration changes."""
        details = {
            "config_type": "model_routing",
            "changes": self._calculate_config_diff(old_config, new_config),
            "affected_agents": list(new_config.get("routing", {}).keys())
        }
        
        self.log_admin_action(
            action=AuditAction.MODEL_CONFIG_UPDATE,
            user=user,
            details=details,
            severity=AuditSeverity.INFO,
            ip_address=ip_address
        )
    
    def log_provider_config_change(
        self,
        user: User,
        provider_name: str,
        action_type: str,  # "add", "update", "delete"
        masked_details: Dict[str, Any],
        ip_address: Optional[str] = None
    ) -> None:
        """Log cloud provider configuration changes."""
        details = {
            "provider_name": provider_name,
            "action_type": action_type,
            "config_details": masked_details  # Should not contain actual API keys
        }
        
        action_map = {
            "add": AuditAction.PROVIDER_CONFIG_ADD,
            "update": AuditAction.PROVIDER_CONFIG_UPDATE,
            "delete": AuditAction.PROVIDER_CONFIG_DELETE
        }
        
        self.log_admin_action(
            action=action_map.get(action_type, AuditAction.PROVIDER_CONFIG_UPDATE),
            user=user,
            details=details,
            severity=AuditSeverity.WARNING,  # Provider changes are more sensitive
            ip_address=ip_address
        )
    
    def log_security_event(
        self,
        user: User,
        event_type: str,
        details: Dict[str, Any],
        ip_address: Optional[str] = None
    ) -> None:
        """Log security-related events."""
        security_details = {
            "event_type": event_type,
            "security_context": details,
            "risk_level": self._assess_risk_level(event_type, details)
        }
        
        self.log_admin_action(
            action=AuditAction.ADMIN_LOGIN,  # Generic security action
            user=user,
            details=security_details,
            severity=AuditSeverity.WARNING,
            ip_address=ip_address
        )
    
    def _calculate_config_diff(
        self,
        old_config: Dict[str, Any],
        new_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate differences between configurations."""
        changes = {}
        
        # Compare routing configurations
        old_routing = old_config.get("routing", {})
        new_routing = new_config.get("routing", {})
        
        for agent, new_agent_config in new_routing.items():
            old_agent_config = old_routing.get(agent, {})
            
            agent_changes = {}
            for key, new_value in new_agent_config.items():
                old_value = old_agent_config.get(key)
                if old_value != new_value:
                    agent_changes[key] = {
                        "old": old_value,
                        "new": new_value
                    }
            
            if agent_changes:
                changes[agent] = agent_changes
        
        return changes
    
    def _assess_risk_level(self, event_type: str, details: Dict[str, Any]) -> str:
        """Assess the risk level of a security event."""
        high_risk_events = [
            "multiple_failed_logins",
            "privilege_escalation",
            "suspicious_ip_access"
        ]
        
        if event_type in high_risk_events:
            return "high"
        elif "failed" in event_type.lower():
            return "medium"
        else:
            return "low"
    
    def get_audit_logs(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[int] = None,
        action_type: Optional[AuditAction] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Retrieve audit logs with filtering.
        
        Args:
            start_date: Start date for log retrieval
            end_date: End date for log retrieval
            user_id: Filter by specific user ID
            action_type: Filter by action type
            limit: Maximum number of logs to return
            
        Returns:
            List of audit log entries
        """
        # This is a simplified implementation
        # In production, you'd want to use a proper log aggregation system
        logs = []
        
        try:
            log_file = self.log_dir / "admin_audit.log"
            if log_file.exists():
                with open(log_file, 'r') as f:
                    for line in f:
                        try:
                            # Parse the log line to extract JSON
                            if " - INFO - " in line:
                                json_part = line.split(" - INFO - ", 1)[1]
                                log_entry = json.loads(json_part)
                                
                                # Apply filters
                                if self._matches_filters(log_entry, start_date, end_date, user_id, action_type):
                                    logs.append(log_entry)
                                    
                                if len(logs) >= limit:
                                    break
                                    
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            print(f"Error reading audit logs: {e}")
        
        return logs
    
    def _matches_filters(
        self,
        log_entry: Dict[str, Any],
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        user_id: Optional[int],
        action_type: Optional[AuditAction]
    ) -> bool:
        """Check if a log entry matches the given filters."""
        # Date filtering
        if start_date or end_date:
            entry_date = datetime.fromisoformat(log_entry["timestamp"])
            if start_date and entry_date < start_date:
                return False
            if end_date and entry_date > end_date:
                return False
        
        # User filtering
        if user_id and log_entry.get("user", {}).get("id") != user_id:
            return False
        
        # Action type filtering
        if action_type and log_entry.get("action") != action_type.value:
            return False
        
        return True


# Global audit logger instance
admin_audit_logger = AdminAuditLogger()
