import React from 'react';
import { Navigate } from 'react-router-dom';
import { FiShield, FiAlertTriangle, FiLoader } from 'react-icons/fi';
import { useAdminAuth } from '../../hooks/useAdminAuth';

interface AdminRouteGuardProps {
  children: React.ReactNode;
  requiredPermission?: string;
  fallbackPath?: string;
}

/**
 * Route guard component that ensures only admin users can access protected routes
 */
const AdminRouteGuard: React.FC<AdminRouteGuardProps> = ({
  children,
  requiredPermission,
  fallbackPath = '/dashboard'
}) => {
  const { isAdmin, isLoading, error, hasPermission } = useAdminAuth();

  // Show loading state while checking admin status
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Verifying Admin Access
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Please wait while we verify your administrative privileges...
          </p>
        </div>
      </div>
    );
  }

  // Show error state if admin check failed
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FiAlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Access Verification Failed
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            We couldn't verify your administrative privileges. Please try logging in again.
          </p>
          <button
            onClick={() => window.location.href = '/login'}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Return to Login
          </button>
        </div>
      </div>
    );
  }

  // Redirect if user is not admin
  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FiShield className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Admin Access Required
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            You don't have administrative privileges to access this page. 
            Contact your system administrator if you believe this is an error.
          </p>
          <div className="space-y-2">
            <button
              onClick={() => window.location.href = fallbackPath}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Return to Dashboard
            </button>
            <button
              onClick={() => window.location.href = '/logout'}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check specific permission if required
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FiShield className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Insufficient Permissions
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            You don't have the required permission "{requiredPermission}" to access this feature.
          </p>
          <button
            onClick={() => window.location.href = '/admin'}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Return to Admin Dashboard
          </button>
        </div>
      </div>
    );
  }

  // User is admin and has required permissions - render children
  return <>{children}</>;
};

export default AdminRouteGuard;
