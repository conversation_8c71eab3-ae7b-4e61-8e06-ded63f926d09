# AI Coding Agent Environment Configuration
# Copy this file to .env and fill in your actual values

# Application Settings
APP_NAME=AI Coding Agent
APP_VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
HOST=localhost
PORT=8000
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_coding_agent
DB_USER=postgres
DB_PASSWORD=your_secure_password_here

# Security Settings
SECRET_KEY=your_super_secret_key_that_must_be_at_least_32_characters_long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# AI Service Configuration
OLLAMA_HOST=http://localhost:11434
DEFAULT_AI_MODEL=mistral:7b-instruct-q4_0

# Specialized Agent Models (6 total agents)
ARCHITECT_AGENT_MODEL=llama3.2:3b
FRONTEND_AGENT_MODEL=starcoder2:3b
BACKEND_AGENT_MODEL=deepseek-coder:6.7b-instruct
SHELL_AGENT_MODEL=qwen2.5:3b
DEBUG_AGENT_MODEL=deepseek-coder:6.7b-instruct
TEST_AGENT_MODEL=qwen2.5:3b

# Code-specific task models
CODE_COMPLETION_MODEL=starcoder2:3b
CODE_GENERATION_MODEL=deepseek-coder:6.7b-instruct
CODE_REVIEW_MODEL=deepseek-coder:6.7b-instruct

# Chat and specialized models
CHAT_MODEL=llama3.2:3b
DOCUMENTATION_MODEL=llama3.2:3b

# AI Performance settings
AI_MAX_TOKENS=4096
AI_TEMPERATURE=0.7
