#!/usr/bin/env python3
"""
Test FastAPI App and Routes

This script verifies that the FastAPI application loads correctly
and displays all available routes.
"""

import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_fastapi_app():
    """Test FastAPI application loading and route configuration."""
    try:
        print("🔧 Testing FastAPI app creation...")
        from ai_coding_agent.main import app
        print("✅ FastAPI app created successfully!")

        print("\n🛣️  Available routes:")
        for route in app.routes:
            if hasattr(route, 'path'):
                methods = ""
                if hasattr(route, 'methods'):
                    methods = f"{list(route.methods)}"
                path = getattr(route, 'path', 'unknown')
                print(f"  {methods:<20} {path}")

        print("\n🤖 Testing AI service imports...")
        from ai_coding_agent.services.ai import AIProvider, AgentOrchestrator
        from ai_coding_agent.services.ai.providers.ollama import OllamaProvider
        print("✅ All AI services import successfully!")

        print("\n🎯 Testing agent configurations...")
        from ai_coding_agent.agents import AGENT_CONFIGS, AgentRole
        print(f"✅ {len(AGENT_CONFIGS)} agents configured:")
        for role, config in AGENT_CONFIGS.items():
            print(f"  - {role.value}: {config.model}")

        print("\n🚀 All systems ready for Phase 4: AI Integration Foundation!")
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fastapi_app()
    sys.exit(0 if success else 1)
