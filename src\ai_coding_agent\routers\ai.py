"""
AI API Router

Provides API endpoints for AI agent interactions, orchestration, and management.
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import json

from ..agents import AgentRole, AgentCapability
from ..services.ai import AgentOrchestrator
from ..services.ai.base import Chat<PERSON>essage, ChatRequest
from ..services.ai.orchestrator import TaskRequest, TaskResult, AgentCollaboration

router = APIRouter(prefix="/api/v1/ai", tags=["AI"])

# Global orchestrator instance
orchestrator = AgentOrchestrator()


# Request/Response Models
class ChatRequestModel(BaseModel):
    """Request model for chat completion."""
    message: str
    agent_role: Optional[AgentRole] = None
    conversation_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    stream: bool = False
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None


class ChatResponseModel(BaseModel):
    """Response model for chat completion."""
    model_config = {"protected_namespaces": ()}

    response: str
    agent_role: Optional[AgentRole] = None
    conversation_id: Optional[str] = None
    model_used: str
    tokens_used: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class TaskRequestModel(BaseModel):
    """Request model for task execution."""
    task_description: str
    preferred_agent: Optional[AgentRole] = None
    required_capabilities: List[AgentCapability] = Field(default_factory=list)
    context: Optional[Dict[str, Any]] = None
    conversation_id: Optional[str] = None


class CollaborationRequestModel(BaseModel):
    """Request model for multi-agent collaboration."""
    task_description: str
    primary_agent: AgentRole
    supporting_agents: List[AgentRole] = Field(default_factory=list)
    coordination_strategy: str = "sequential"
    context: Optional[Dict[str, Any]] = None


class ConversationCreateModel(BaseModel):
    """Request model for creating a conversation."""
    agent_role: Optional[AgentRole] = None
    project_context: Optional[Dict[str, Any]] = None


class MessageModel(BaseModel):
    """Model for conversation messages."""
    role: str
    content: str
    timestamp: str


class ConversationModel(BaseModel):
    """Model for conversation details."""
    conversation_id: str
    agent_role: Optional[AgentRole] = None
    created_at: str
    last_activity: str
    message_count: int
    recent_messages: List[MessageModel] = Field(default_factory=list)


# Enhanced Task Execution with Verification
class EnhancedTaskRequestModel(BaseModel):
    """Enhanced request model for task execution with verification and auto-fix."""
    task_description: str
    preferred_agent: Optional[AgentRole] = None
    required_capabilities: List[AgentCapability] = Field(default_factory=list)
    context: Optional[Dict[str, Any]] = None
    conversation_id: Optional[str] = None
    verify_output: bool = True
    auto_fix: bool = True


class VerificationRequestModel(BaseModel):
    """Request model for code verification."""
    code: str
    agent_type: AgentRole
    context: Optional[str] = None


class DependencyInstallRequest(BaseModel):
    """Request model for dependency installation."""
    packages: List[str]
    context: Optional[Dict[str, Any]] = None


# Dependency to get current user (placeholder for now)
async def get_current_user() -> Optional[str]:
    """Get current user ID. This should be replaced with actual auth."""
    return "default_user"


@router.post("/chat", response_model=ChatResponseModel)
async def chat_completion(
    request: ChatRequestModel,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Generate a chat completion using the specified or auto-selected agent.
    """
    try:
        # Create task request
        task_request = TaskRequest(
            task_description=request.message,
            preferred_agent=request.agent_role,
            context=request.context,
            user_id=user_id,
            conversation_id=request.conversation_id
        )

        # Execute task
        result = await orchestrator.execute_task(task_request)

        if not result.success:
            raise HTTPException(status_code=500, detail=result.error)

        return ChatResponseModel(
            response=result.result or "",
            agent_role=result.agent_role,
            conversation_id=result.metadata.get("conversation_id") if result.metadata else None,
            model_used=result.metadata.get("model_used", "unknown") if result.metadata else "unknown",
            tokens_used=result.metadata.get("tokens_used") if result.metadata else None,
            metadata=result.metadata
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat completion failed: {str(e)}")


@router.post("/chat/stream")
async def stream_chat_completion(
    request: ChatRequestModel,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Generate a streaming chat completion using the specified or auto-selected agent.
    """
    try:
        # Get or create conversation
        conversation_id = request.conversation_id
        if not conversation_id:
            conversation_id = await orchestrator.conversation_manager.create_conversation(
                agent_role=request.agent_role,
                user_id=user_id,
                project_context=request.context
            )

        # Add user message
        await orchestrator.conversation_manager.add_message(
            conversation_id, "user", request.message
        )

        # Get conversation messages
        messages = await orchestrator.conversation_manager.get_conversation_messages(
            conversation_id, limit=10
        )

        # Get provider and create chat request
        provider = orchestrator.providers.get("ollama")
        if not provider:
            raise HTTPException(status_code=500, detail="AI provider not available")

        chat_request = ChatRequest(
            messages=messages,
            agent_role=request.agent_role,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            stream=True,
            context=request.context
        )

        async def generate():
            """Generate streaming response."""
            full_response = ""
            try:
                async for chunk in provider.stream_chat(chat_request):
                    full_response += chunk
                    yield f"data: {json.dumps({'content': chunk, 'conversation_id': conversation_id})}\n\n"

                # Add assistant response to conversation
                await orchestrator.conversation_manager.add_message(
                    conversation_id, "assistant", full_response
                )

                yield f"data: {json.dumps({'done': True, 'conversation_id': conversation_id})}\n\n"

            except Exception as e:
                yield f"data: {json.dumps({'error': str(e)})}\n\n"

        return StreamingResponse(
            generate(),
            media_type="text/plain",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Streaming chat failed: {str(e)}")


@router.post("/tasks", response_model=TaskResult)
async def execute_task(
    request: TaskRequestModel,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Execute a task using intelligent agent selection and routing.
    """
    try:
        task_request = TaskRequest(
            task_description=request.task_description,
            preferred_agent=request.preferred_agent,
            required_capabilities=request.required_capabilities,
            context=request.context,
            user_id=user_id,
            conversation_id=request.conversation_id
        )

        result = await orchestrator.execute_task(task_request)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Task execution failed: {str(e)}")


@router.post("/execute-enhanced-task", response_model=TaskResult)
async def execute_enhanced_task(
    request: EnhancedTaskRequestModel,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Execute a task with enhanced features including verification and auto-fix.

    Features:
    - Automatic agent selection based on task description
    - Output verification by Architect Agent
    - Auto-fixing of issues when detected
    - Dependency detection and installation
    """
    try:
        task_request = TaskRequest(
            task_description=request.task_description,
            preferred_agent=request.preferred_agent,
            required_capabilities=request.required_capabilities,
            context=request.context,
            user_id=user_id,
            conversation_id=request.conversation_id,
            verify_output=request.verify_output,
            auto_fix=request.auto_fix
        )

        result = await orchestrator.execute_task(task_request)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/collaborate", response_model=List[TaskResult])
async def collaborate_agents(
    request: CollaborationRequestModel,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Execute a task using multiple collaborating agents.
    """
    try:
        task_request = TaskRequest(
            task_description=request.task_description,
            context=request.context,
            user_id=user_id
        )

        collaboration = AgentCollaboration(
            primary_agent=request.primary_agent,
            supporting_agents=request.supporting_agents,
            coordination_strategy=request.coordination_strategy
        )

        results = await orchestrator.collaborate_agents(task_request, collaboration)
        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Agent collaboration failed: {str(e)}")


@router.post("/collaborate-enhanced")
async def collaborate_agents_enhanced(
    request: CollaborationRequestModel,
    verification_enabled: bool = True,
    auto_dependency_management: bool = True,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Execute a task using multiple collaborating agents with enhanced features.

    This endpoint orchestrates multiple agents with:
    - Cross-agent verification and quality checks
    - Automatic dependency management
    - Context sharing between agents
    - Sequential, parallel, or hierarchical coordination
    """
    try:
        task_request = TaskRequest(
            task_description=request.task_description,
            context=request.context,
            user_id=user_id,
            verify_output=verification_enabled,
            auto_fix=True
        )

        collaboration = AgentCollaboration(
            primary_agent=request.primary_agent,
            supporting_agents=request.supporting_agents,
            coordination_strategy=request.coordination_strategy,
            verification_enabled=verification_enabled,
            auto_dependency_management=auto_dependency_management
        )

        results = await orchestrator.collaborate_agents(task_request, collaboration)
        return {"results": results, "total_agents": len(results)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/verify-code")
async def verify_code(
    request: VerificationRequestModel,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Verify code quality using the Architect Agent.

    This endpoint provides detailed code review and suggestions for improvement.
    """
    try:
        task_request = TaskRequest(
            task_description=f"""
Please review and verify this {request.agent_type.value} code:

```
{request.code}
```

{f'Context: {request.context}' if request.context else ''}

Provide detailed feedback including:
1. Code quality assessment (score 0.0-1.0)
2. Security considerations
3. Best practice adherence
4. Performance implications
5. Specific improvement suggestions

Format your response as:
SCORE: [0.0-1.0]
ISSUES:
- [List any issues found]
SUGGESTIONS:
- [List improvement suggestions]
""",
            preferred_agent=AgentRole.ARCHITECT,
            user_id=user_id,
            verify_output=False,
            auto_fix=False
        )

        result = await orchestrator.execute_task(task_request)

        return {
            "verification_result": result.result,
            "success": result.success,
            "agent_role": result.agent_role,
            "error": result.error
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/install-dependencies")
async def install_dependencies(
    request: DependencyInstallRequest,
    user_id: Optional[str] = Depends(get_current_user)
):
    """
    Install Python packages using the Shell Agent.

    This endpoint safely installs packages in the current environment
    with proper error handling and feedback.
    """
    try:
        task_request = TaskRequest(
            task_description=f"""
Install the following Python packages safely:
{chr(10).join(f'- {pkg}' for pkg in request.packages)}

For each package:
1. Check if already installed
2. Install if needed using pip
3. Verify successful installation
4. Report any errors or conflicts

Provide detailed feedback on the installation process.
""",
            preferred_agent=AgentRole.SHELL,
            context=request.context,
            user_id=user_id,
            verify_output=False,
            auto_fix=False
        )

        result = await orchestrator.execute_task(task_request)

        return {
            "status": "completed" if result.success else "failed",
            "packages": request.packages,
            "result": result.result,
            "error": result.error,
            "dependencies_installed": result.dependencies_installed
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/agents", response_model=List[Dict[str, Any]])
async def get_agents():
    """
    Get information about all available agents.
    """
    try:
        from ..agents import AGENT_CONFIGS

        agents = []
        for role, config in AGENT_CONFIGS.items():
            # Handle capabilities - they might be strings or enum values
            capabilities = []
            for cap in config.capabilities:
                if hasattr(cap, 'value'):
                    capabilities.append(cap.value)
                else:
                    capabilities.append(str(cap))

            agents.append({
                "role": role.value,
                "model": config.model,
                "description": config.description,
                "capabilities": capabilities,
                "temperature": config.temperature,
                "max_tokens": config.max_tokens
            })

        return agents

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get agents: {str(e)}")


@router.get("/agents/health", response_model=Dict[str, Any])
async def get_agent_health():
    """
    Get health status for all agents and their models.
    """
    try:
        health = await orchestrator.get_agent_health()
        return health

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get agent health: {str(e)}")


@router.get("/models", response_model=List[Dict[str, Any]])
async def get_models():
    """
    Get information about available AI models.
    """
    try:
        provider = orchestrator.providers.get("ollama")
        if not provider:
            raise HTTPException(status_code=500, detail="AI provider not available")

        models = await provider.get_models()

        return [
            {
                "name": model.name,
                "size": model.size,
                "capabilities": model.capabilities,
                "agent_roles": [role.value for role in model.agent_roles],
                "context_window": model.context_window,
                "max_tokens": model.max_tokens
            }
            for model in models
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_orchestrator_stats():
    """
    Get orchestrator statistics and performance metrics.
    """
    try:
        stats = await orchestrator.get_orchestrator_stats()
        return stats

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Check the health of the AI service.
    """
    try:
        provider = orchestrator.providers.get("ollama")
        if not provider:
            return {"status": "unhealthy", "error": "No AI provider available"}

        health = await provider.health_check()

        return {
            "status": health.status.value,
            "provider": health.provider_name,
            "models": [
                {
                    "model": model.model,
                    "status": model.status.value,
                    "latency_ms": model.latency_ms,
                    "error": model.error
                }
                for model in health.models
            ],
            "last_checked": health.last_checked.isoformat()
        }

    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}
