#!/usr/bin/env python3
"""
Test to verify roadmap models can be imported without SQLAlchemy metadata conflicts.
"""

def test_model_imports():
    """Test that all roadmap models can be imported successfully."""
    try:
        print("🧪 Testing roadmap model imports...")

        # Test individual model imports
        from src.ai_coding_agent.models.roadmap import TaskStatus, AgentType
        print("✅ Enums imported successfully")

        from src.ai_coding_agent.models.roadmap import Project
        print("✅ Project model imported successfully")

        from src.ai_coding_agent.models.roadmap import Roadmap
        print("✅ Roadmap model imported successfully")

        from src.ai_coding_agent.models.roadmap import Phase
        print("✅ Phase model imported successfully")

        from src.ai_coding_agent.models.roadmap import Step
        print("✅ Step model imported successfully")

        from src.ai_coding_agent.models.roadmap import Task
        print("✅ Task model imported successfully")

        # Test all models together
        from src.ai_coding_agent.models.roadmap import (
            Project, Roadmap, Phase, Step, Task,
            TaskStatus, AgentType
        )
        print("✅ All roadmap models imported together successfully")

        # Test via models __init__
        from src.ai_coding_agent.models import (
            Project, Roadmap, Phase, Step, Task,
            TaskStatus, AgentType
        )
        print("✅ All models imported via models.__init__ successfully")

        # Test model instantiation (should not require database)
        status = TaskStatus.PENDING
        agent = AgentType.ARCHITECT
        print(f"✅ Enums work: status={status}, agent={agent}")

        print("\n🎉 SUCCESS: All SQLAlchemy metadata conflicts have been resolved!")
        print("   The 'metadata' fields have been properly renamed to 'project_metadata'")
        return True

    except Exception as e:
        print(f"❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_model_imports()
    if success:
        print("\n✅ Phase B1 roadmap models are ready for use!")
        exit(0)
    else:
        print("\n❌ Phase B1 roadmap models have issues that need to be fixed.")
        exit(1)
