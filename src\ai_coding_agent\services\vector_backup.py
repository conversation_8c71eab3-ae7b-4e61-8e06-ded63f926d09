"""
Vector Database Backup System for LTKB

Simple backup and restore functionality for ChromaDB data during development.
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import zipfile

from .vector_db import VectorDBClient, EmbeddingNamespace


class VectorDBBackup:
    """Simple backup system for vector database."""

    def __init__(self, vector_db: VectorDBClient, backup_dir: str = "backups"):
        self.vector_db = vector_db
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)

    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """Create a backup of the vector database."""
        if backup_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"ltkb_backup_{timestamp}"

        backup_path = self.backup_dir / f"{backup_name}.zip"

        # Create backup zip file
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Backup ChromaDB persist directory
            persist_dir = Path(self.vector_db.persist_directory)
            if persist_dir.exists():
                for file_path in persist_dir.rglob("*"):
                    if file_path.is_file():
                        arcname = file_path.relative_to(persist_dir.parent)
                        zipf.write(file_path, arcname)

            # Backup LTKB metadata
            ltkb_metadata_path = Path("ltkb/metadata.json")
            if ltkb_metadata_path.exists():
                zipf.write(ltkb_metadata_path, "ltkb_metadata.json")

            # Create backup manifest
            manifest = {
                "backup_name": backup_name,
                "created_at": datetime.now().isoformat(),
                "vector_db_path": str(persist_dir),
                "ltkb_metadata_included": ltkb_metadata_path.exists(),
                "version": "A1"
            }

            zipf.writestr("backup_manifest.json", json.dumps(manifest, indent=2))

        print(f"✅ Backup created: {backup_path}")
        return str(backup_path)

    def restore_backup(self, backup_path: str, confirm: bool = False) -> bool:
        """Restore from a backup file."""
        if not confirm:
            print("⚠️  This will overwrite existing vector database data!")
            response = input("Are you sure? (yes/no): ")
            if response.lower() != "yes":
                print("❌ Restore cancelled")
                return False

        backup_file = Path(backup_path)
        if not backup_file.exists():
            print(f"❌ Backup file not found: {backup_path}")
            return False

        try:
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                # Extract to temporary directory first
                temp_dir = self.backup_dir / "temp_restore"
                temp_dir.mkdir(exist_ok=True)
                zipf.extractall(temp_dir)

                # Read manifest
                manifest_path = temp_dir / "backup_manifest.json"
                if manifest_path.exists():
                    with open(manifest_path, 'r') as f:
                        manifest = json.load(f)
                    print(f"📦 Restoring backup: {manifest['backup_name']}")
                    print(f"📅 Created: {manifest['created_at']}")

                # Restore vector database
                persist_dir = Path(self.vector_db.persist_directory)
                if persist_dir.exists():
                    shutil.rmtree(persist_dir)

                vector_db_backup = temp_dir / "vector_db"
                if vector_db_backup.exists():
                    shutil.copytree(vector_db_backup, persist_dir)

                # Restore LTKB metadata
                ltkb_metadata_backup = temp_dir / "ltkb_metadata.json"
                if ltkb_metadata_backup.exists():
                    ltkb_dir = Path("ltkb")
                    ltkb_dir.mkdir(exist_ok=True)
                    shutil.copy2(ltkb_metadata_backup, ltkb_dir / "metadata.json")

                # Cleanup
                shutil.rmtree(temp_dir)

            print("✅ Backup restored successfully")
            return True

        except Exception as e:
            print(f"❌ Error restoring backup: {e}")
            return False

    def list_backups(self) -> list:
        """List available backups."""
        backups = []
        for backup_file in self.backup_dir.glob("*.zip"):
            try:
                with zipfile.ZipFile(backup_file, 'r') as zipf:
                    manifest_data = zipf.read("backup_manifest.json")
                    manifest = json.loads(manifest_data)
                    backups.append({
                        "file": str(backup_file),
                        "name": manifest["backup_name"],
                        "created_at": manifest["created_at"],
                        "size": backup_file.stat().st_size
                    })
            except Exception:
                # Skip invalid backup files
                continue

        return sorted(backups, key=lambda x: x["created_at"], reverse=True)

    def auto_backup(self, keep_last: int = 5) -> str:
        """Create automatic backup and cleanup old ones."""
        backup_path = self.create_backup()

        # Cleanup old backups
        backups = self.list_backups()
        if len(backups) > keep_last:
            for old_backup in backups[keep_last:]:
                old_file = Path(old_backup["file"])
                if old_file.exists():
                    old_file.unlink()
                    print(f"🗑️  Removed old backup: {old_backup['name']}")

        return backup_path


# Convenience functions
def create_vector_db_backup(backup_name: Optional[str] = None) -> str:
    """Create a backup of the vector database."""
    from .vector_db import get_vector_db

    vector_db = get_vector_db()
    backup_system = VectorDBBackup(vector_db)
    return backup_system.create_backup(backup_name)


def restore_vector_db_backup(backup_path: str, confirm: bool = False) -> bool:
    """Restore vector database from backup."""
    from .vector_db import get_vector_db

    vector_db = get_vector_db()
    backup_system = VectorDBBackup(vector_db)
    return backup_system.restore_backup(backup_path, confirm)


if __name__ == "__main__":
    # CLI interface for backup operations
    import sys

    if len(sys.argv) < 2:
        print("Usage:")
        print("  python vector_backup.py create [backup_name]")
        print("  python vector_backup.py restore <backup_path>")
        print("  python vector_backup.py list")
        sys.exit(1)

    command = sys.argv[1]

    if command == "create":
        backup_name = sys.argv[2] if len(sys.argv) > 2 else None
        backup_path = create_vector_db_backup(backup_name)
        print(f"Backup created: {backup_path}")

    elif command == "restore":
        if len(sys.argv) < 3:
            print("Error: backup path required")
            sys.exit(1)
        backup_path = sys.argv[2]
        success = restore_vector_db_backup(backup_path)
        sys.exit(0 if success else 1)

    elif command == "list":
        from .vector_db import get_vector_db
        vector_db = get_vector_db()
        backup_system = VectorDBBackup(vector_db)
        backups = backup_system.list_backups()

        if not backups:
            print("No backups found")
        else:
            print("Available backups:")
            for backup in backups:
                size_mb = backup["size"] / (1024 * 1024)
                print(f"  {backup['name']} - {backup['created_at']} ({size_mb:.1f} MB)")

    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
