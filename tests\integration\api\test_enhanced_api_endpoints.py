"""
API Endpoint Tests for Enhanced Dependency System
Tests for new API endpoints added in the dependency system improvements.
"""

import pytest
from datetime import datetime
from uuid import uuid4

def test_enhanced_dependency_api_structure():
    """Test that enhanced dependency API endpoints can be imported."""
    try:
        # Test that we can import the enhanced models
        from src.ai_coding_agent.models import (
            ConditionalDependency, BatchDependencyCheckRequest,
            DependencyBottleneck, VisualizationNode, ExternalToolConfig
        )
        
        # Test that we can import the enhanced dependency engine
        from src.ai_coding_agent.services.dependency_engine import DependencyEngine
        
        # Test that we can import the roadmap service
        from src.ai_coding_agent.services.roadmap import RoadmapService
        
        print("✅ Enhanced dependency API structure test passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False


def test_conditional_dependency_evaluation():
    """Test conditional dependency evaluation logic."""
    try:
        from src.ai_coding_agent.services.dependency_engine import ConditionEvaluator
        from src.ai_coding_agent.models import (
            ConditionEvaluationContext, ConditionalDependency, 
            ConditionType, DependencyType
        )
        
        evaluator = ConditionEvaluator()
        
        # Create test context
        context = ConditionEvaluationContext(
            platform="web",
            environment="production",
            feature_flags={"new_feature": True},
            user_choices={"theme": "dark"}
        )
        
        # Create conditional dependency
        conditional_dep = ConditionalDependency(
            dependency_id="dep-123",
            dependency_type=DependencyType.TASK,
            condition="platform == 'web' and feature_flags['new_feature'] == True",
            condition_type=ConditionType.PLATFORM,
            description="Web platform with new feature enabled"
        )
        
        # Evaluate the conditional dependency
        result = evaluator.evaluate_conditional_dependency(conditional_dep, context)
        
        assert result.condition_met is True
        assert result.dependency_id == "dep-123"
        assert len(result.errors) == 0
        
        print("✅ Conditional dependency evaluation test passed")
        return True
        
    except Exception as e:
        print(f"❌ Conditional dependency evaluation test failed: {str(e)}")
        return False


def test_batch_dependency_operations():
    """Test batch dependency operations."""
    try:
        from src.ai_coding_agent.models import (
            BatchDependencyCheckRequest, DependencyType,
            ConditionEvaluationContext
        )
        
        # Create batch request
        context = ConditionEvaluationContext(platform="web")
        
        batch_request = BatchDependencyCheckRequest(
            entity_ids=["task-1", "task-2", "task-3"],
            entity_type=DependencyType.TASK,
            include_soft_dependencies=True,
            include_conditional_dependencies=True,
            evaluation_context=context,
            user_id="test-user"
        )
        
        # Validate batch request structure
        assert len(batch_request.entity_ids) == 3
        assert batch_request.entity_type == DependencyType.TASK
        assert batch_request.include_soft_dependencies is True
        assert batch_request.user_id == "test-user"
        
        print("✅ Batch dependency operations test passed")
        return True
        
    except Exception as e:
        print(f"❌ Batch dependency operations test failed: {str(e)}")
        return False


def test_dependency_analytics():
    """Test dependency analytics functionality."""
    try:
        from src.ai_coding_agent.models import (
            DependencyBottleneck, DependencyMetrics, DelayPrediction,
            DependencyType
        )
        
        # Test bottleneck creation
        bottleneck = DependencyBottleneck(
            entity_id="task-123",
            entity_type=DependencyType.TASK,
            entity_name="Critical Task",
            bottleneck_type="blocking",
            severity="high",
            affected_entities=["task-124", "task-125"],
            estimated_delay=8,
            resolution_suggestions=["Prioritize task", "Add resources"]
        )
        
        assert bottleneck.severity == "high"
        assert len(bottleneck.affected_entities) == 2
        
        # Test metrics creation
        metrics = DependencyMetrics(
            roadmap_id="roadmap-123",
            timeframe_start=datetime.utcnow(),
            timeframe_end=datetime.utcnow(),
            total_dependencies_checked=100,
            dependencies_resolved=85,
            average_resolution_time=4.5
        )
        
        assert metrics.total_dependencies_checked == 100
        assert metrics.dependencies_resolved == 85
        
        # Test delay prediction
        prediction = DelayPrediction(
            entity_id="phase-123",
            entity_type=DependencyType.PHASE,
            entity_name="Development Phase",
            predicted_delay=16,
            confidence=0.8,
            delay_causes=["Resource constraint"],
            mitigation_strategies=["Add developers"],
            impact_on_project="Minor delay"
        )
        
        assert prediction.predicted_delay == 16
        assert prediction.confidence == 0.8
        
        print("✅ Dependency analytics test passed")
        return True
        
    except Exception as e:
        print(f"❌ Dependency analytics test failed: {str(e)}")
        return False


def test_visualization_data_structures():
    """Test visualization data structures."""
    try:
        from src.ai_coding_agent.models import (
            VisualizationNode, VisualizationEdge, DependencyGraphVisualization,
            GanttChartTask, GanttChart, DashboardWidget, DependencyDashboard,
            DependencyType
        )
        
        # Test visualization node
        node = VisualizationNode(
            id="node-1",
            label="Task 1",
            type=DependencyType.TASK,
            status="in_progress",
            x=100.0,
            y=200.0,
            color="#6bcf7f",
            size=15
        )
        
        assert node.label == "Task 1"
        assert node.x == 100.0
        
        # Test visualization edge
        edge = VisualizationEdge(
            source="node-1",
            target="node-2",
            type="dependency",
            color="#666666"
        )
        
        assert edge.source == "node-1"
        assert edge.type == "dependency"
        
        # Test dependency graph visualization
        graph_viz = DependencyGraphVisualization(
            roadmap_id="roadmap-123",
            title="Test Graph",
            nodes=[node],
            edges=[edge],
            layout="hierarchical"
        )
        
        assert graph_viz.title == "Test Graph"
        assert len(graph_viz.nodes) == 1
        assert len(graph_viz.edges) == 1
        
        # Test Gantt chart task
        gantt_task = GanttChartTask(
            id="task-1",
            name="Implement Feature",
            start_date=datetime.utcnow(),
            end_date=datetime.utcnow(),
            duration=8,
            status="in_progress"
        )
        
        assert gantt_task.name == "Implement Feature"
        assert gantt_task.duration == 8
        
        print("✅ Visualization data structures test passed")
        return True
        
    except Exception as e:
        print(f"❌ Visualization data structures test failed: {str(e)}")
        return False


def test_external_tool_integration():
    """Test external tool integration models."""
    try:
        from src.ai_coding_agent.models import (
            ExternalToolType, ExternalToolConfig, ExternalToolSyncStatus,
            ExternalToolSyncResult, JiraIssueMapping, GitHubIssueMapping
        )
        
        # Test external tool config
        tool_config = ExternalToolConfig(
            tool_type=ExternalToolType.JIRA,
            name="JIRA Integration",
            base_url="https://company.atlassian.net",
            api_key="test-key",
            project_id="PROJ"
        )
        
        assert tool_config.tool_type == ExternalToolType.JIRA
        assert tool_config.name == "JIRA Integration"
        
        # Test sync result
        sync_result = ExternalToolSyncResult(
            tool_config_id="config-123",
            sync_type="bidirectional",
            status=ExternalToolSyncStatus.SUCCESS,
            items_processed=25,
            sync_duration=30.5
        )
        
        assert sync_result.status == ExternalToolSyncStatus.SUCCESS
        assert sync_result.items_processed == 25
        
        # Test JIRA mapping
        jira_mapping = JiraIssueMapping(
            task_id="task-123",
            jira_issue_key="PROJ-456",
            jira_issue_id="10123",
            sync_direction="bidirectional"
        )
        
        assert jira_mapping.task_id == "task-123"
        assert jira_mapping.jira_issue_key == "PROJ-456"
        
        # Test GitHub mapping
        github_mapping = GitHubIssueMapping(
            task_id="task-124",
            github_issue_number=42,
            github_repo="owner/repo",
            sync_direction="export"
        )
        
        assert github_mapping.task_id == "task-124"
        assert github_mapping.github_issue_number == 42
        
        print("✅ External tool integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ External tool integration test failed: {str(e)}")
        return False


def test_dependency_engine_enhancements():
    """Test dependency engine enhancements."""
    try:
        from src.ai_coding_agent.services.dependency_engine import (
            DependencyEngine, DependencyCache, ConditionEvaluator
        )
        
        # Test dependency cache
        cache = DependencyCache()
        assert hasattr(cache, 'dependency_graph_cache')
        assert hasattr(cache, 'check_result_cache')
        assert hasattr(cache, 'condition_cache')
        
        # Test condition evaluator
        evaluator = ConditionEvaluator()
        assert hasattr(evaluator, 'safe_operators')
        assert hasattr(evaluator, 'evaluate_condition')
        
        # Test that DependencyEngine has new methods
        # Note: We can't instantiate without a database session
        assert hasattr(DependencyEngine, 'check_conditional_dependencies')
        assert hasattr(DependencyEngine, 'batch_check_dependencies')
        assert hasattr(DependencyEngine, 'analyze_bottlenecks')
        assert hasattr(DependencyEngine, 'generate_dependency_graph_visualization')
        assert hasattr(DependencyEngine, 'create_jira_integration')
        
        print("✅ Dependency engine enhancements test passed")
        return True
        
    except Exception as e:
        print(f"❌ Dependency engine enhancements test failed: {str(e)}")
        return False


def test_api_endpoint_structure():
    """Test that API endpoints can be imported and have correct structure."""
    try:
        # Test that we can import the roadmap router
        from src.ai_coding_agent.routers import roadmap
        
        # Check that the router has the expected attributes
        assert hasattr(roadmap, 'router')
        
        # The router should be a FastAPI router
        from fastapi import APIRouter
        assert isinstance(roadmap.router, APIRouter)
        
        print("✅ API endpoint structure test passed")
        return True
        
    except Exception as e:
        print(f"❌ API endpoint structure test failed: {str(e)}")
        return False


def main():
    """Run all enhanced API endpoint tests."""
    print("🚀 Starting Enhanced Dependency System API Tests\n")
    
    tests = [
        test_enhanced_dependency_api_structure,
        test_conditional_dependency_evaluation,
        test_batch_dependency_operations,
        test_dependency_analytics,
        test_visualization_data_structures,
        test_external_tool_integration,
        test_dependency_engine_enhancements,
        test_api_endpoint_structure,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            print(f"📋 Running {test.__name__}...")
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {str(e)}")
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL ENHANCED API TESTS PASSED!")
        print("✅ Enhanced dependency system APIs are working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
