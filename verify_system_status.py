#!/usr/bin/env python3
"""
AI Coding Agent - Complete System Status Verification
Final check to verify all systems are operational and Phase B1 is complete
"""

def verify_complete_system():
    """Comprehensive system verification."""
    print('🔍 AI Coding Agent - Complete System Status Verification')
    print('=' * 60)

    # Test imports
    print('1️⃣ Testing core imports...')
    try:
        from src.ai_coding_agent.config import settings
        from src.ai_coding_agent.models.base import get_hybrid_db_manager
        from src.ai_coding_agent.models.roadmap import Project, Roadmap, Phase, Step, Task
        from src.ai_coding_agent.services.roadmap import RoadmapService
        from src.ai_coding_agent.services.vector_db import get_vector_db
        from src.ai_coding_agent.services.supabase import get_supabase_service
        print('✅ All core modules imported successfully')
    except Exception as e:
        print(f'❌ Import failed: {e}')
        return False

    # Test database connections
    print('2️⃣ Testing database connections...')
    try:
        db_manager = get_hybrid_db_manager()
        status = db_manager.get_connection_status()

        print(f'  🔹 Mode: {status["mode"]}')
        print(f'  🔹 Local DB: {status["local_db"]}')
        print(f'  🔹 Supabase: {status.get("supabase", "not configured")}')

        if status['local_db'] != 'connected':
            print('❌ Local database not connected')
            return False

        print('✅ Database connections verified')
    except Exception as e:
        print(f'❌ Database connection test failed: {e}')
        return False

    # Test service layer
    print('3️⃣ Testing service layer...')
    try:
        db_session = next(db_manager.get_local_db())
        try:
            roadmap_service = RoadmapService(db_session)
            print('✅ Roadmap service operational')
        finally:
            db_session.close()

        vector_service = get_vector_db()
        print('✅ Vector database service operational')

        # Test Supabase service (optional)
        try:
            supabase_service = get_supabase_service()
            print('✅ Supabase service operational')
        except Exception as e:
            print(f'⚠️ Supabase service: {e}')

    except Exception as e:
        print(f'❌ Service layer test failed: {e}')
        return False

    # Test configuration
    print('4️⃣ Testing configuration...')
    try:
        print(f'  🔹 Environment: {settings.environment}')
        print(f'  🔹 Debug mode: {settings.debug}')
        print('✅ Configuration loaded successfully')
    except Exception as e:
        print(f'❌ Configuration test failed: {e}')
        return False

    # Test Phase B1 functionality
    print('5️⃣ Testing Phase B1: Roadmap System...')
    try:
        # Verify tables exist
        db_manager.create_local_tables()

        # Test basic roadmap operations
        db_session = next(db_manager.get_local_db())
        try:
            roadmap_service = RoadmapService(db_session)

            # Create a test project
            from src.ai_coding_agent.models import ProjectCreate

            test_project = ProjectCreate(
                name="System Verification Test",
                description="Testing Phase B1 functionality",
                tech_stack={"languages": ["python"], "frameworks": ["fastapi"]}
            )

            created_project = roadmap_service.create_project(test_project)
            print(f'✅ Created test project: {created_project.name}')

            # Clean up test data
            roadmap_service.delete_project(created_project.id)
            print('✅ Cleaned up test data')

        finally:
            db_session.close()

        print('✅ Phase B1: Roadmap System is fully operational')
    except Exception as e:
        print(f'❌ Phase B1 test failed: {e}')
        return False

    # System summary
    print('\n🎯 System Status Summary')
    print('=' * 40)
    print('✅ Core modules: OPERATIONAL')
    print('✅ Database systems: OPERATIONAL')
    print('✅ Service layer: OPERATIONAL')
    print('✅ Configuration: OPERATIONAL')
    print('✅ Phase B1: Roadmap System: COMPLETE')
    print()
    print('🚀 AI Coding Agent is ready for production!')
    print('📋 Phase B1: Roadmap System Implementation - COMPLETE')
    print('🔄 Ready for Phase B2: Dependency Engine & Phase Locking')

    return True

if __name__ == "__main__":
    success = verify_complete_system()
    exit(0 if success else 1)
