"""
Roadmap Service Layer
Implements business logic for roadmap management and Phase B1 functionality.
"""

from datetime import datetime
from typing import Dict, List, Optional, Tuple
from uuid import uuid4

from sqlalchemy.orm import Session, selectinload
from sqlalchemy import select, and_, or_
from fastapi import HTT<PERSON>Exception
from fastapi import status as http_status

from ..models import (
    Project, Roadmap, Phase, Step, Task,
    TaskStatus, AgentType,
    ProjectCreate, ProjectUpdate, ProjectResponse,
    RoadmapCreate, RoadmapUpdate, RoadmapResponse,
    PhaseCreate, PhaseUpdate, PhaseResponse,
    StepCreate, StepUpdate, StepResponse,
    TaskCreate, TaskUpdate, TaskResponse,
    AuditAction, AuditEntityType,
    DependencyType, StatusBubbleEvent,
)
from .audit import AuditService
from .schema_validation import schema_validator
from .dependency_engine import DependencyEngine


class RoadmapService:
    """Service layer for roadmap management with audit trail support."""

    def __init__(self, db: Session, user_id: Optional[str] = None, user_email: Optional[str] = None):
        self.db = db
        self.user_id = user_id
        self.user_email = user_email
        self.audit_service = AuditService(db)
        self.dependency_engine = DependencyEngine(db, user_id)

    # Project CRUD Operations

    def create_project(self, project_data: ProjectCreate) -> ProjectResponse:
        """Create a new project with optional roadmap."""
        try:
            # Create project
            project = Project(
                id=str(uuid4()),
                name=project_data.name,
                description=project_data.description,
                tech_stack=project_data.tech_stack,
                project_rules=project_data.project_rules,
            )

            self.db.add(project)
            self.db.flush()  # Get project ID without committing

            # Create roadmap if provided
            if project_data.roadmap:
                roadmap = self._create_roadmap_for_project(
                    project.id,
                    project_data.roadmap
                )
                project.roadmap = roadmap

            self.db.commit()
            self.db.refresh(project)

            return ProjectResponse.model_validate(project)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create project: {str(e)}"
            )

    def get_project(self, project_id: str) -> ProjectResponse:
        """Get project by ID with full roadmap data."""
        project = (
            self.db.execute(
                select(Project)
                .options(selectinload(Project.roadmap).selectinload(Roadmap.phases).selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Project.id == project_id)
            )
            .scalar_one_or_none()
        )

        if not project:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Project {project_id} not found"
            )

        return ProjectResponse.model_validate(project)

    def update_project(self, project_id: str, update_data: ProjectUpdate) -> ProjectResponse:
        """Update project information."""
        project = self.db.execute(
            select(Project).where(Project.id == project_id)
        ).scalar_one_or_none()

        if not project:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Project {project_id} not found"
            )

        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(project, field, value)

        project.updated_at = datetime.utcnow()

        try:
            self.db.commit()
            self.db.refresh(project)
            return ProjectResponse.model_validate(project)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update project: {str(e)}"
            )

    def delete_project(self, project_id: str) -> bool:
        """Delete project and all associated data."""
        project = self.db.execute(
            select(Project).where(Project.id == project_id)
        ).scalar_one_or_none()

        if not project:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Project {project_id} not found"
            )

        try:
            self.db.delete(project)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete project: {str(e)}"
            )

    # Roadmap CRUD Operations

    def create_roadmap(self, project_id: str, roadmap_data: RoadmapCreate) -> RoadmapResponse:
        """Create a roadmap for an existing project."""
        # Verify project exists
        project = self.db.execute(
            select(Project).where(Project.id == project_id)
        ).scalar_one_or_none()

        if not project:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Project {project_id} not found"
            )

        # Check if roadmap already exists
        existing_roadmap = self.db.execute(
            select(Roadmap).where(Roadmap.project_id == project_id)
        ).scalar_one_or_none()

        if existing_roadmap:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"Project {project_id} already has a roadmap"
            )

        try:
            roadmap = self._create_roadmap_for_project(project_id, roadmap_data)
            self.db.commit()
            self.db.refresh(roadmap)

            return RoadmapResponse.model_validate(roadmap)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create roadmap: {str(e)}"
            )

    def create_standalone_roadmap(self, roadmap_data: RoadmapCreate) -> RoadmapResponse:
        """Create a standalone roadmap without an existing project."""
        try:
            # Validate roadmap schema if provided
            self._validate_roadmap_schema(roadmap_data)

            # Create a new project for this roadmap
            project = Project(
                id=str(uuid4()),
                name=f"Project for {roadmap_data.name}",
                description=f"Auto-generated project for roadmap: {roadmap_data.name}",
                tech_stack=roadmap_data.project_metadata.get("tech_stack") if roadmap_data.project_metadata else None,
            )

            self.db.add(project)
            self.db.flush()

            # Create the roadmap
            roadmap = self._create_roadmap_for_project(project.id, roadmap_data)
            project.roadmap = roadmap

            self.db.commit()
            self.db.refresh(roadmap)

            # Log audit action
            if self.audit_service:
                self.audit_service.log_action(
                    entity_type=AuditEntityType.ROADMAP,
                    entity_id=roadmap.id,
                    action=AuditAction.CREATE,
                    user_id=self.user_id,
                    user_email=self.user_email,
                    new_values={
                        "name": roadmap.name,
                        "version": roadmap.version,
                        "project_id": roadmap.project_id,
                        "phases_count": len(roadmap_data.phases)
                    },
                    reason="Standalone roadmap creation"
                )

            return RoadmapResponse.model_validate(roadmap)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create standalone roadmap: {str(e)}"
            )

    def get_roadmap(self, roadmap_id: str) -> RoadmapResponse:
        """Get roadmap by ID with full hierarchy."""
        roadmap = (
            self.db.execute(
                select(Roadmap)
                .options(selectinload(Roadmap.phases).selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Roadmap.id == roadmap_id)
            )
            .scalar_one_or_none()
        )

        if not roadmap:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Roadmap {roadmap_id} not found"
            )

        return RoadmapResponse.model_validate(roadmap)

    def get_project_roadmap(self, project_id: str) -> RoadmapResponse:
        """Get roadmap for a specific project."""
        roadmap = (
            self.db.execute(
                select(Roadmap)
                .options(selectinload(Roadmap.phases).selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Roadmap.project_id == project_id)
            )
            .scalar_one_or_none()
        )

        if not roadmap:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"No roadmap found for project {project_id}"
            )

        return RoadmapResponse.model_validate(roadmap)

    def update_roadmap(self, roadmap_id: str, update_data: RoadmapUpdate) -> RoadmapResponse:
        """Update roadmap information."""
        roadmap = self.db.execute(
            select(Roadmap).where(Roadmap.id == roadmap_id)
        ).scalar_one_or_none()

        if not roadmap:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Roadmap {roadmap_id} not found"
            )

        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(roadmap, field, value)

        roadmap.updated_at = datetime.utcnow()

        try:
            self.db.commit()
            self.db.refresh(roadmap)
            return RoadmapResponse.model_validate(roadmap)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update roadmap: {str(e)}"
            )

    def delete_roadmap(self, roadmap_id: str) -> bool:
        """Delete roadmap and optionally its associated project."""
        try:
            # Get roadmap with project info
            roadmap = (
                self.db.execute(
                    select(Roadmap)
                    .options(selectinload(Roadmap.project))
                    .where(Roadmap.id == roadmap_id)
                )
                .scalar_one_or_none()
            )

            if not roadmap:
                raise HTTPException(
                    status_code=http_status.HTTP_404_NOT_FOUND,
                    detail=f"Roadmap {roadmap_id} not found"
                )

            # Log audit action before deletion
            if self.audit_service:
                self.audit_service.log_action(
                    entity_type=AuditEntityType.ROADMAP,
                    entity_id=roadmap_id,
                    action=AuditAction.DELETE,
                    user_id=self.user_id,
                    user_email=self.user_email,
                    old_values={
                        "name": roadmap.name,
                        "version": roadmap.version,
                        "project_id": roadmap.project_id,
                        "status": roadmap.status
                    },
                    reason="Roadmap deletion"
                )

            # Delete the roadmap (cascade will handle phases, steps, tasks)
            self.db.delete(roadmap)

            # If this was an auto-generated project (name starts with "Project for"), delete it too
            if roadmap.project and roadmap.project.name.startswith("Project for"):
                self.db.delete(roadmap.project)

            self.db.commit()
            return True

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete roadmap: {str(e)}"
            )

    # Task Status Management

    def update_task_status(self, task_id: str, status: TaskStatus,
                          artifacts: Optional[List[Dict]] = None,
                          error_message: Optional[str] = None,
                          reason: Optional[str] = None) -> TaskResponse:
        """Update task status and propagate changes up the hierarchy with audit logging."""
        task = self.db.execute(
            select(Task).where(Task.id == task_id)
        ).scalar_one_or_none()

        if not task:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )

        try:
            # Capture old values for audit
            old_values = {
                "status": task.status,
                "artifacts": task.artifacts,
                "error_message": task.error_message,
                "started_at": task.started_at,
                "completed_at": task.completed_at
            }

            # Update task status
            old_status = task.status
            task.status = status
            task.updated_at = datetime.utcnow()

            if status == TaskStatus.IN_PROGRESS and old_status == TaskStatus.PENDING:
                task.started_at = datetime.utcnow()
            elif status == TaskStatus.COMPLETED and old_status != TaskStatus.COMPLETED:
                task.completed_at = datetime.utcnow()

            if artifacts:
                task.artifacts = artifacts

            if error_message:
                task.error_message = error_message

            self.db.flush()

            # Log status change in audit trail
            if old_status != status:
                self.audit_service.log_status_change(
                    entity_type=AuditEntityType.TASK,
                    entity_id=task_id,
                    old_status=old_status,
                    new_status=status,
                    user_id=self.user_id,
                    triggered_by="user" if self.user_id else "system",
                    reason=reason,
                    metadata={
                        "artifacts_added": len(artifacts) if artifacts else 0,
                        "has_error": bool(error_message)
                    }
                )

            # Log general audit action
            new_values = {
                "status": task.status,
                "artifacts": task.artifacts,
                "error_message": task.error_message,
                "started_at": task.started_at,
                "completed_at": task.completed_at
            }

            self.audit_service.log_action(
                entity_type=AuditEntityType.TASK,
                entity_id=task_id,
                action=AuditAction.STATUS_CHANGE,
                user_id=self.user_id,
                user_email=self.user_email,
                old_values=old_values,
                new_values=new_values,
                reason=reason
            )

            # Bubble up status changes and process automatic progression
            self._bubble_up_status_changes(task)

            # Process automatic progression if task was completed
            if status == TaskStatus.COMPLETED:
                progression_events = self.dependency_engine.process_automatic_progression(
                    task_id, DependencyType.TASK
                )

                # Log progression events
                for event in progression_events:
                    self.audit_service.log_status_change(
                        entity_type=AuditEntityType.TASK if event.source_entity_type == DependencyType.TASK
                                   else AuditEntityType.STEP if event.source_entity_type == DependencyType.STEP
                                   else AuditEntityType.PHASE,
                        entity_id=event.source_entity_id,
                        old_status=event.old_status,
                        new_status=event.new_status,
                        user_id=self.user_id,
                        triggered_by="automatic_progression",
                        reason="Automatic progression triggered by dependency engine",
                        metadata={
                            "triggered_by_entity": task_id,
                            "progression_event_id": event.source_entity_id,
                            "propagation_path": event.propagation_path
                        }
                    )

            self.db.commit()
            self.db.refresh(task)

            return TaskResponse.model_validate(task)

        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update task status: {str(e)}"
            )

    def get_task_status(self, task_id: str) -> Dict:
        """Get detailed task status information."""
        task = (
            self.db.execute(
                select(Task)
                .options(selectinload(Task.step).selectinload(Step.phase).selectinload(Phase.roadmap))
                .where(Task.id == task_id)
            )
            .scalar_one_or_none()
        )

        if not task:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )

        return {
            "task": TaskResponse.model_validate(task),
            "step": StepResponse.model_validate(task.step),
            "phase": PhaseResponse.model_validate(task.step.phase),
            "roadmap": RoadmapResponse.model_validate(task.step.phase.roadmap),
        }

    # Project Analysis and Metrics

    def get_project_progress(self, project_id: str) -> Dict:
        """Get comprehensive project progress metrics."""
        project = self.get_project(project_id)

        if not project.roadmap:
            return {
                "project_id": project_id,
                "overall_progress": 0.0,
                "phases": [],
                "total_tasks": 0,
                "completed_tasks": 0,
                "in_progress_tasks": 0,
                "blocked_tasks": 0,
                "failed_tasks": 0,
            }

        # Calculate metrics across all tasks
        all_tasks = []
        phase_metrics = []

        for phase in project.roadmap.phases:
            phase_tasks = []
            for step in phase.steps:
                phase_tasks.extend(step.tasks)
                all_tasks.extend(step.tasks)

            phase_progress = self._calculate_phase_progress(phase_tasks)
            phase_metrics.append({
                "phase_id": phase.id,
                "phase_name": phase.name,
                "progress": phase_progress,
                "status": phase.status,
                "task_count": len(phase_tasks),
            })

        # Overall metrics
        total_tasks = len(all_tasks)
        completed_tasks = sum(1 for task in all_tasks if task.status == TaskStatus.COMPLETED)
        in_progress_tasks = sum(1 for task in all_tasks if task.status == TaskStatus.IN_PROGRESS)
        blocked_tasks = sum(1 for task in all_tasks if task.status == TaskStatus.BLOCKED)
        failed_tasks = sum(1 for task in all_tasks if task.status == TaskStatus.FAILED)

        overall_progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0.0

        return {
            "project_id": project_id,
            "overall_progress": round(overall_progress, 2),
            "phases": phase_metrics,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "in_progress_tasks": in_progress_tasks,
            "blocked_tasks": blocked_tasks,
            "failed_tasks": failed_tasks,
        }

    # Helper Methods

    def _create_roadmap_for_project(self, project_id: str, roadmap_data: RoadmapCreate) -> Roadmap:
        """Create roadmap with full hierarchy for a project."""
        roadmap = Roadmap(
            id=str(uuid4()),
            project_id=project_id,
            name=roadmap_data.name,
            version=roadmap_data.version,
            project_metadata=roadmap_data.project_metadata,
        )

        self.db.add(roadmap)
        self.db.flush()

        # Create phases
        for phase_idx, phase_data in enumerate(roadmap_data.phases):
            phase = Phase(
                id=str(uuid4()),
                roadmap_id=roadmap.id,
                name=phase_data.name,
                description=phase_data.description,
                order_index=phase_data.order_index,
                dependencies=phase_data.dependencies,
                estimated_duration=phase_data.estimated_duration,
                project_metadata=phase_data.project_metadata,
            )

            self.db.add(phase)
            self.db.flush()

            # Create steps
            for step_idx, step_data in enumerate(phase_data.steps):
                step = Step(
                    id=str(uuid4()),
                    phase_id=phase.id,
                    name=step_data.name,
                    description=step_data.description,
                    order_index=step_data.order_index,
                    dependencies=step_data.dependencies,
                    estimated_duration=step_data.estimated_duration,
                    project_metadata=step_data.project_metadata,
                )

                self.db.add(step)
                self.db.flush()

                # Create tasks
                for task_idx, task_data in enumerate(step_data.tasks):
                    task = Task(
                        id=str(uuid4()),
                        step_id=step.id,
                        name=task_data.name,
                        description=task_data.description,
                        order_index=task_data.order_index,
                        assigned_agent=task_data.assigned_agent,
                        dependencies=task_data.dependencies,
                        estimated_duration=task_data.estimated_duration,
                        project_metadata=task_data.project_metadata,
                    )

                    self.db.add(task)

        return roadmap

    def _bubble_up_status_changes(self, task: Task) -> None:
        """Bubble up status changes from task to step to phase to roadmap."""
        # Get step with all tasks
        step = self.db.execute(
            select(Step)
            .options(selectinload(Step.tasks))
            .where(Step.id == task.step_id)
        ).scalar_one()

        # Update step status based on task statuses
        old_step_status = step.status
        step.status = self._calculate_container_status([t.status for t in step.tasks])
        step.updated_at = datetime.utcnow()

        if old_step_status != step.status:
            # Log step status change
            self.audit_service.log_status_change(
                entity_type=AuditEntityType.STEP,
                entity_id=step.id,
                old_status=old_step_status,
                new_status=step.status,
                user_id=self.user_id,
                triggered_by="system",
                reason="Status bubbled up from task changes",
                metadata={
                    "triggered_by_task": task.id,
                    "task_count": len(step.tasks),
                    "completed_tasks": len([t for t in step.tasks if t.status == TaskStatus.COMPLETED])
                }
            )

            # Get phase with all steps
            phase = self.db.execute(
                select(Phase)
                .options(selectinload(Phase.steps).selectinload(Step.tasks))
                .where(Phase.id == step.phase_id)
            ).scalar_one()

            # Update phase status based on step statuses
            old_phase_status = phase.status
            phase.status = self._calculate_container_status([s.status for s in phase.steps])
            phase.updated_at = datetime.utcnow()

            if old_phase_status != phase.status:
                # Log phase status change
                self.audit_service.log_status_change(
                    entity_type=AuditEntityType.PHASE,
                    entity_id=phase.id,
                    old_status=old_phase_status,
                    new_status=phase.status,
                    user_id=self.user_id,
                    triggered_by="system",
                    reason="Status bubbled up from step changes",
                    metadata={
                        "triggered_by_step": step.id,
                        "step_count": len(phase.steps),
                        "completed_steps": len([s for s in phase.steps if s.status == TaskStatus.COMPLETED])
                    }
                )

                # Get roadmap with all phases
                roadmap = self.db.execute(
                    select(Roadmap)
                    .options(selectinload(Roadmap.phases))
                    .where(Roadmap.id == phase.roadmap_id)
                ).scalar_one()

                # Update roadmap status based on phase statuses
                old_roadmap_status = roadmap.status
                roadmap.status = self._calculate_container_status([p.status for p in roadmap.phases])
                roadmap.updated_at = datetime.utcnow()

                if old_roadmap_status != roadmap.status:
                    # Log roadmap status change
                    self.audit_service.log_status_change(
                        entity_type=AuditEntityType.ROADMAP,
                        entity_id=roadmap.id,
                        old_status=old_roadmap_status,
                        new_status=roadmap.status,
                        user_id=self.user_id,
                        triggered_by="system",
                        reason="Status bubbled up from phase changes",
                        metadata={
                            "triggered_by_phase": phase.id,
                            "phase_count": len(roadmap.phases),
                            "completed_phases": len([p for p in roadmap.phases if p.status == TaskStatus.COMPLETED])
                        }
                    )

    def _calculate_container_status(self, child_statuses: List[str]) -> TaskStatus:
        """Calculate container status based on children statuses."""
        if not child_statuses:
            return TaskStatus.PENDING

        status_counts = {status: child_statuses.count(status) for status in set(child_statuses)}
        total = len(child_statuses)

        # If any failed, container is failed
        if status_counts.get(TaskStatus.FAILED, 0) > 0:
            return TaskStatus.FAILED

        # If any blocked, container is blocked
        if status_counts.get(TaskStatus.BLOCKED, 0) > 0:
            return TaskStatus.BLOCKED

        # If all completed, container is completed
        if status_counts.get(TaskStatus.COMPLETED, 0) == total:
            return TaskStatus.COMPLETED

        # If any in progress, container is in progress
        if status_counts.get(TaskStatus.IN_PROGRESS, 0) > 0:
            return TaskStatus.IN_PROGRESS

        # Otherwise, still pending
        return TaskStatus.PENDING

    def _calculate_phase_progress(self, tasks: List) -> float:
        """Calculate progress percentage for a phase."""
        if not tasks:
            return 0.0

        completed = sum(1 for task in tasks if task.status == TaskStatus.COMPLETED)
        return (completed / len(tasks)) * 100

    def start_task(self, task_id: str, force_override: bool = False, override_reason: Optional[str] = None) -> TaskResponse:
        """Start a task by updating its status to IN_PROGRESS with dependency validation."""
        # Validate dependencies before starting
        validation = self.dependency_engine.validate_task_execution(
            task_id, "start", force_override, override_reason
        )

        if not validation.allowed:
            error_msg = f"Cannot start task: {validation.result.message}"
            if validation.errors:
                error_msg += f". Errors: {', '.join(validation.errors)}"
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )

        # Log warnings if any
        if validation.warnings:
            print(f"Task start warnings: {', '.join(validation.warnings)}")

        return self.update_task_status(task_id, TaskStatus.IN_PROGRESS)

    def complete_task(self, task_id: str, artifacts: Optional[List[Dict]] = None, force_override: bool = False, override_reason: Optional[str] = None) -> TaskResponse:
        """Complete a task by updating its status to COMPLETED with dependency validation."""
        # For completion, we're more lenient - just log warnings
        validation = self.dependency_engine.validate_task_execution(
            task_id, "complete", force_override, override_reason
        )

        # Log warnings if any
        if validation.warnings:
            print(f"Task completion warnings: {', '.join(validation.warnings)}")

        return self.update_task_status(task_id, TaskStatus.COMPLETED, artifacts=artifacts)

    def get_tasks_by_agent(self, agent_type: AgentType) -> List[TaskResponse]:
        """Get all tasks assigned to a specific agent type."""
        tasks = self.db.execute(
            select(Task).where(Task.assigned_agent == agent_type)
        ).scalars().all()

        return [TaskResponse.model_validate(task) for task in tasks]

    # Dependency Engine Integration Methods

    def check_task_dependencies(self, task_id: str) -> Dict:
        """Check task dependencies using the dependency engine."""
        result = self.dependency_engine.can_start_task(task_id)
        return {
            "can_start": result.can_start,
            "status": result.status.value,
            "message": result.message,
            "blocking_dependencies": [dep.model_dump() for dep in result.blocking_dependencies],
            "warnings": result.warnings,
            "override_level": result.override_level.value
        }

    def check_step_dependencies(self, step_id: str) -> Dict:
        """Check step dependencies using the dependency engine."""
        result = self.dependency_engine.can_start_step(step_id)
        return {
            "can_start": result.can_start,
            "status": result.status.value,
            "message": result.message,
            "blocking_dependencies": [dep.model_dump() for dep in result.blocking_dependencies],
            "warnings": result.warnings,
            "override_level": result.override_level.value
        }

    def check_phase_dependencies(self, phase_id: str) -> Dict:
        """Check phase dependencies using the dependency engine."""
        result = self.dependency_engine.can_start_phase(phase_id)
        return {
            "can_start": result.can_start,
            "status": result.status.value,
            "message": result.message,
            "blocking_dependencies": [dep.model_dump() for dep in result.blocking_dependencies],
            "warnings": result.warnings,
            "override_level": result.override_level.value
        }

    def get_phase_progression_status(self, phase_id: str) -> Dict:
        """Get phase progression status using the dependency engine."""
        result = self.dependency_engine.check_phase_progression(phase_id)
        return result.model_dump()

    def validate_execution_order(self, entity_id: str, entity_type: str) -> Dict:
        """Validate execution order for an entity."""
        from ..models import DependencyType

        # Convert string to enum
        if entity_type.lower() == "task":
            dep_type = DependencyType.TASK
        elif entity_type.lower() == "step":
            dep_type = DependencyType.STEP
        elif entity_type.lower() == "phase":
            dep_type = DependencyType.PHASE
        else:
            raise ValueError(f"Invalid entity type: {entity_type}")

        is_valid, warnings = self.dependency_engine.validate_execution_order(entity_id, dep_type)
        return {
            "is_valid": is_valid,
            "warnings": warnings,
            "entity_id": entity_id,
            "entity_type": entity_type
        }

    # Schema Validation Methods

    def _validate_roadmap_schema(self, roadmap_data: RoadmapCreate) -> None:
        """Validate roadmap data against JSON schema."""
        # Convert Pydantic model to dict for schema validation
        roadmap_dict = roadmap_data.model_dump()

        # Add required fields that might be missing
        if "project_id" not in roadmap_dict:
            roadmap_dict["project_id"] = "00000000-0000-0000-0000-000000000000"  # Placeholder

        # Validate against schema
        schema_validator.validate_roadmap_strict(roadmap_dict)

    def validate_roadmap_json(self, roadmap_json: Dict) -> Tuple[bool, List[str]]:
        """Validate a roadmap JSON object against the schema."""
        return schema_validator.validate_roadmap(roadmap_json)
