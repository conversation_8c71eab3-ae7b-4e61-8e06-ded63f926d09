"""
Platform-specific functionality for cross-platform isolation infrastructure.

This module provides platform detection, path management, and permission handling
for Windows, macOS, and Linux systems.
"""

from .detector import PlatformDetector, get_platform_info, SupportedPlatform
from .paths import PlatformPaths, get_platform_paths
from .permissions import PlatformPermissions, set_platform_permissions

__all__ = [
    'PlatformDetector',
    'get_platform_info',
    'SupportedPlatform',
    'PlatformPaths',
    'get_platform_paths',
    'PlatformPermissions',
    'set_platform_permissions'
]
