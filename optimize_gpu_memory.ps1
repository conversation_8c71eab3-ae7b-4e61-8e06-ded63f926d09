#!/usr/bin/env pwsh
# Ollama GPU Memory Optimization Script for Quadro P1000 (4GB)

Write-Host "🚀 Optimizing Ollama for 4GB GPU Memory Usage" -ForegroundColor Green
Write-Host "=" * 60

# Step 1: Check current GPU usage
Write-Host "`n📊 Current GPU Status:" -ForegroundColor Yellow
nvidia-smi --query-gpu=name,memory.total,memory.used,memory.free --format=csv

# Step 2: Stop current Ollama processes
Write-Host "`n🛑 Stopping current Ollama processes..." -ForegroundColor Yellow
Get-Process -Name "ollama" -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 3

# Step 3: Set environment variables for maximum GPU usage
Write-Host "`n⚙️ Setting Ollama environment variables for 4GB optimization..." -ForegroundColor Yellow
$env:OLLAMA_GPU_MEMORY_FRACTION = "0.95"
$env:OLLAMA_MAX_LOADED_MODELS = "2"
$env:OLLAMA_MAX_QUEUE = "8"
$env:OLLAMA_NUM_PARALLEL = "2"
$env:OLLAMA_FLASH_ATTENTION = "1"
$env:OLLAMA_KEEP_ALIVE = "10m"

Write-Host "  ✅ OLLAMA_GPU_MEMORY_FRACTION = 0.95 (use 95% of 4GB)"
Write-Host "  ✅ OLLAMA_MAX_LOADED_MODELS = 2 (keep 2 models in GPU memory)"
Write-Host "  ✅ OLLAMA_NUM_PARALLEL = 2 (handle 2 concurrent requests)"
Write-Host "  ✅ OLLAMA_FLASH_ATTENTION = 1 (enable memory-efficient attention)"

# Step 4: Start Ollama server in background
Write-Host "`n🔄 Starting optimized Ollama server..." -ForegroundColor Yellow
Start-Process -FilePath "ollama" -ArgumentList "serve" -WindowStyle Hidden
Start-Sleep -Seconds 5

# Step 5: Preload optimal model combination for 4GB
Write-Host "`n🧠 Preloading optimal model combination..." -ForegroundColor Yellow

# Strategy: Load two 3B models that together use ~3.7GB (optimal for 4GB)
Write-Host "  Loading llama3.2:3b (Architect Agent) ..."
ollama run llama3.2:3b "Hello! Ready for architect tasks." | Out-Null

Write-Host "  Loading starcoder2:3b (Frontend Agent) ..."
ollama run starcoder2:3b "Hello! Ready for frontend tasks." | Out-Null

# Step 6: Check memory usage after optimization
Write-Host "`n📈 GPU Memory Usage After Optimization:" -ForegroundColor Green
nvidia-smi --query-gpu=memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits | ForEach-Object {
    $values = $_ -split ','
    $used = [int]$values[0].Trim()
    $total = [int]$values[1].Trim()
    $utilization = [int]$values[2].Trim()
    $percentage = [math]::Round(($used / $total) * 100, 1)

    Write-Host "  🎯 Memory Usage: $used MB / $total MB ($percentage%)" -ForegroundColor Cyan
    Write-Host "  ⚡ GPU Utilization: $utilization%" -ForegroundColor Cyan

    if ($percentage -gt 85) {
        Write-Host "  ✅ Excellent! Using >85% of GPU memory" -ForegroundColor Green
    } elseif ($percentage -gt 70) {
        Write-Host "  ⚠️ Good utilization, can optimize further" -ForegroundColor Yellow
    } else {
        Write-Host "  ❌ Low utilization, check configuration" -ForegroundColor Red
    }
}

# Step 7: List loaded models
Write-Host "`n📋 Currently Loaded Models:" -ForegroundColor Yellow
ollama ps

# Step 8: Test performance with quick requests
Write-Host "`n🧪 Testing Model Performance..." -ForegroundColor Yellow

$startTime = Get-Date
$response = ollama run llama3.2:3b "Write a simple function in Python" --no-interactive
$endTime = Get-Date
$duration = ($endTime - $startTime).TotalSeconds

Write-Host "  🏃‍♂️ llama3.2:3b response time: $([math]::Round($duration, 2)) seconds" -ForegroundColor Cyan

$startTime = Get-Date
$response = ollama run starcoder2:3b "Create a React component" --no-interactive
$endTime = Get-Date
$duration = ($endTime - $startTime).TotalSeconds

Write-Host "  🏃‍♂️ starcoder2:3b response time: $([math]::Round($duration, 2)) seconds" -ForegroundColor Cyan

Write-Host "`n✅ Ollama GPU optimization complete!" -ForegroundColor Green
Write-Host "🎯 Your Quadro P1000 is now optimally configured for maximum performance" -ForegroundColor Green

# Display optimization summary
Write-Host "`n📊 Optimization Summary:" -ForegroundColor Magenta
Write-Host "  • GPU Memory Fraction: 95% of 4GB"
Write-Host "  • Max Loaded Models: 2 (optimal for concurrent processing)"
Write-Host "  • Parallel Requests: 2 (balanced for 4GB)"
Write-Host "  • Flash Attention: Enabled (memory efficient)"
Write-Host "  • Keep Alive: 10 minutes (reduces reload overhead)"

Write-Host "`n💡 Tips for Maximum Performance:" -ForegroundColor Cyan
Write-Host "  • Use llama3.2:3b + starcoder2:3b combination for best balance"
Write-Host "  • Load deepseek-coder:6.7b only for complex tasks (uses ~3.8GB alone)"
Write-Host "  • Monitor with: nvidia-smi -l 2"
Write-Host "  • Run this script again to re-optimize if needed"
