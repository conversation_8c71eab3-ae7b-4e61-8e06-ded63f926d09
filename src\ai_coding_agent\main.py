"""
FastAPI main application for AI Coding Agent.

This module sets up the FastAPI application with security middleware,
CORS configuration, and API routing.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from .config import settings
from .routers import health, auth, ai, ltkb, roadmap, supabase_auth, admin
from .utils.logging import configure_logging
from .services.audit import setup_audit_serialization_hooks


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.

    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    configure_logging()
    setup_audit_serialization_hooks()  # 🛡️ Enable bulletproof JSON serialization
    print(f"🚀 Starting {settings.app_name} v{settings.version}")
    print(f"🌍 Environment: {settings.environment}")
    print(f"🔧 Debug mode: {settings.debug}")
    print("🛡️ Audit serialization hooks enabled")

    yield

    # Shutdown
    print("🛑 Shutting down AI Coding Agent")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    app = FastAPI(
        title=settings.app_name,
        version=settings.version,
        description="Intelligent no-code platform with AI agent orchestration",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan,
    )

    # Security middleware - more permissive for testing
    if settings.environment != "testing":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", settings.host],
        )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
        allow_headers=["*"],
    )

    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with basic application info."""
        return {
            "message": f"Welcome to {settings.app_name}",
            "version": settings.version,
            "environment": settings.environment,
            "docs": "/docs" if settings.debug else "disabled",
            "health": "/api/v1/health",
        }

    # Include routers
    app.include_router(health.router, prefix="/api/v1", tags=["health"])

    # Primary authentication (Supabase-based)
    app.include_router(supabase_auth.router, prefix="/api/v1/auth", tags=["authentication"])

    # Legacy authentication (for backward compatibility)
    app.include_router(auth.router, prefix="/api/v1/legacy-auth", tags=["legacy-auth"])

    # Application routers
    app.include_router(ai.router, tags=["ai"])
    app.include_router(ltkb.router, tags=["ltkb"])
    app.include_router(roadmap.router, tags=["roadmap"])
    app.include_router(admin.router, tags=["admin"])

    @app.exception_handler(Exception)
    async def global_exception_handler(
        request: Request, exc: Exception
    ) -> JSONResponse:
        """Global exception handler for unhandled errors."""
        if settings.debug:
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "detail": str(exc),
                    "type": type(exc).__name__,
                },
            )
        return JSONResponse(status_code=500, content={"error": "Internal server error"})

    return app


# Create the application instance
app = create_app()


def run_dev_server() -> None:
    """Run the development server with hot reload."""
    uvicorn.run(
        "ai_coding_agent.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="debug" if settings.debug else "info",
    )


if __name__ == "__main__":
    run_dev_server()
