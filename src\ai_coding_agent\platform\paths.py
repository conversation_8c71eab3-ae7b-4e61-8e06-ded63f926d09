"""
Platform-specific path management for cross-platform workspace isolation.

This module provides path management functionality for Windows, macOS, and Linux
systems with proper directory structure and validation.
"""

import os
from pathlib import Path
from typing import Dict, Optional
from dataclasses import dataclass
import logging

from .detector import get_platform_info, SupportedPlatform

logger = logging.getLogger(__name__)


@dataclass
class PlatformPaths:
    """Platform-specific path configuration."""
    base_dir: Path
    projects_dir: Path
    active_dir: Path
    archived_dir: Path
    logs_dir: Path
    temp_dir: Path

    def __post_init__(self):
        """Ensure all paths are Path objects."""
        for field_name in ['base_dir', 'projects_dir', 'active_dir',
                          'archived_dir', 'logs_dir', 'temp_dir']:
            value = getattr(self, field_name)
            if isinstance(value, str):
                setattr(self, field_name, Path(value))


class PlatformPathManager:
    """Manages platform-specific paths and directory structures."""

    def __init__(self):
        self._paths: Optional[PlatformPaths] = None

    def get_platform_paths(self) -> PlatformPaths:
        """
        Get platform-specific paths for the AI Coding Agent workspace.

        Returns:
            PlatformPaths: Platform-specific directory paths

        Raises:
            RuntimeError: If platform is not supported or paths cannot be determined
        """
        if self._paths is not None:
            return self._paths

        try:
            platform_info = get_platform_info()
            platform_type = platform_info.platform_type

            if platform_type == SupportedPlatform.WINDOWS:
                base_dir = self._get_windows_base_dir()
            elif platform_type == SupportedPlatform.MACOS:
                base_dir = self._get_macos_base_dir()
            elif platform_type == SupportedPlatform.LINUX:
                base_dir = self._get_linux_base_dir()
            else:
                raise RuntimeError(f"Unsupported platform: {platform_type}")

            # Create the full path structure
            projects_dir = base_dir / "Projects"

            self._paths = PlatformPaths(
                base_dir=base_dir,
                projects_dir=projects_dir,
                active_dir=projects_dir / "active",
                archived_dir=projects_dir / "archived",
                logs_dir=projects_dir / "logs",
                temp_dir=projects_dir / "temp"
            )

            logger.info(f"Platform paths configured for {platform_type.value}")
            logger.debug(f"Base directory: {base_dir}")

            return self._paths

        except Exception as e:
            logger.error(f"Failed to determine platform paths: {e}")
            raise RuntimeError(f"Platform path configuration failed: {e}")

    def _get_windows_base_dir(self) -> Path:
        """Get Windows-specific base directory."""
        # Use USERPROFILE environment variable
        user_profile = os.environ.get('USERPROFILE')
        if not user_profile:
            raise RuntimeError("USERPROFILE environment variable not found")

        base_dir = Path(user_profile) / "AppData" / "Local" / "AiCodingAgent"
        logger.debug(f"Windows base directory: {base_dir}")
        return base_dir

    def _get_macos_base_dir(self) -> Path:
        """Get macOS-specific base directory."""
        home_dir = Path.home()
        base_dir = home_dir / ".ai_coding_agent"
        logger.debug(f"macOS base directory: {base_dir}")
        return base_dir

    def _get_linux_base_dir(self) -> Path:
        """Get Linux-specific base directory."""
        home_dir = Path.home()
        base_dir = home_dir / ".ai_coding_agent"
        logger.debug(f"Linux base directory: {base_dir}")
        return base_dir

    def validate_paths(self) -> bool:
        """
        Validate that all required paths are accessible.

        Returns:
            bool: True if all paths are valid and accessible
        """
        if self._paths is None:
            self.get_platform_paths()

        try:
            # Check if base directory parent exists and is writable
            base_parent = self._paths.base_dir.parent
            if not base_parent.exists():
                logger.error(f"Base directory parent does not exist: {base_parent}")
                return False

            if not os.access(base_parent, os.W_OK):
                logger.error(f"Base directory parent is not writable: {base_parent}")
                return False

            logger.info("Platform paths validation successful")
            return True

        except Exception as e:
            logger.error(f"Path validation failed: {e}")
            return False

    def get_project_path(self, project_name: str, active: bool = True) -> Path:
        """
        Get the path for a specific project.

        Args:
            project_name: Name of the project
            active: Whether the project is active (True) or archived (False)

        Returns:
            Path: Full path to the project directory
        """
        if self._paths is None:
            self.get_platform_paths()

        if active:
            return self._paths.active_dir / project_name
        else:
            return self._paths.archived_dir / project_name


# Global path manager instance
_path_manager = PlatformPathManager()


def get_platform_paths() -> PlatformPaths:
    """
    Get platform-specific paths using the global path manager.

    Returns:
        PlatformPaths: Platform-specific directory paths
    """
    return _path_manager.get_platform_paths()
