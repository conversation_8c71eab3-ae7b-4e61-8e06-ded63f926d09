#!/usr/bin/env python3
"""
GPU Memory Optimization Tool for Ollama Models
Analyzes and optimizes GPU memory usage for your 4GB Quadro P1000
"""

import subprocess
import json
import psutil
import time
from typing import Dict, List


def get_gpu_memory_info():
    """Get current GPU memory usage."""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total', '--format=csv,noheader,nounits'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            used, total = map(int, result.stdout.strip().split(', '))
            return used, total
        return None, None
    except:
        return None, None


def get_ollama_models():
    """Get currently loaded Ollama models."""
    try:
        result = subprocess.run(['ollama', 'ps'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 4:
                        model_name = parts[0]
                        size = parts[2]
                        processor = parts[3] if len(parts) > 3 else "N/A"
                        models.append({
                            'name': model_name,
                            'size': size,
                            'processor': processor
                        })
            return models
        return []
    except:
        return []


def analyze_memory_usage():
    """Analyze current memory usage and provide optimization suggestions."""
    used_gpu, total_gpu = get_gpu_memory_info()
    models = get_ollama_models()

    print("🔍 GPU Memory Analysis")
    print("=" * 50)

    if used_gpu and total_gpu:
        print(f"GPU Memory: {used_gpu:,} MB / {total_gpu:,} MB ({used_gpu/total_gpu*100:.1f}%)")
        print(f"Available: {total_gpu - used_gpu:,} MB")
    else:
        print("❌ Could not read GPU memory")

    print(f"\n📊 Loaded Models ({len(models)})")
    print("-" * 30)

    total_model_memory = 0
    cpu_gpu_split_models = []

    for model in models:
        print(f"Model: {model['name']}")
        print(f"  Size: {model['size']}")
        print(f"  Processor: {model['processor']}")

        # Check if model is split between CPU/GPU
        if 'CPU/GPU' in model['processor']:
            cpu_gpu_split_models.append(model)

        # Extract memory size
        size_str = model['size'].replace('GB', '').replace('MB', '')
        try:
            if 'GB' in model['size']:
                size_mb = float(size_str) * 1024
            else:
                size_mb = float(size_str)
            total_model_memory += size_mb
        except:
            pass

        print()

    print(f"Total Model Memory: {total_model_memory:.0f} MB")

    # Provide optimization suggestions
    print("\n💡 Optimization Suggestions")
    print("-" * 30)

    if cpu_gpu_split_models:
        print("⚠️  Models split between CPU/GPU detected!")
        for model in cpu_gpu_split_models:
            print(f"   • {model['name']} - {model['processor']}")
        print("   This reduces performance significantly.")
        print()

    if used_gpu and total_gpu:
        if used_gpu < total_gpu * 0.8:  # Less than 80% usage
            print(f"📈 GPU underutilized: {used_gpu/total_gpu*100:.1f}% usage")
            print("   You can load larger models or multiple models")
        elif used_gpu > total_gpu * 0.95:  # More than 95% usage
            print(f"⚠️  GPU nearly full: {used_gpu/total_gpu*100:.1f}% usage")
            print("   Consider using smaller models or quantized versions")
        else:
            print(f"✅ Good GPU utilization: {used_gpu/total_gpu*100:.1f}% usage")

    return {
        'gpu_used': used_gpu,
        'gpu_total': total_gpu,
        'models': models,
        'cpu_gpu_split': cpu_gpu_split_models,
        'total_model_memory': total_model_memory
    }


def recommend_optimal_models():
    """Recommend optimal model configuration for 4GB GPU."""
    print("\n🎯 Optimal Model Configuration for 4GB Quadro P1000")
    print("=" * 60)

    recommendations = [
        {
            'scenario': 'Single Large Model (Best Quality)',
            'models': ['deepseek-coder:6.7b-instruct'],
            'memory': '~3.8GB',
            'pros': 'Highest quality responses',
            'cons': 'Only one model loaded, slower switching'
        },
        {
            'scenario': 'Multiple Smaller Models (Best Versatility)',
            'models': ['llama3.2:3b', 'starcoder2:3b'],
            'memory': '~3.7GB total',
            'pros': 'Fast switching, specialized models',
            'cons': 'Slightly lower quality for complex tasks'
        },
        {
            'scenario': 'Balanced Approach (Recommended)',
            'models': ['qwen2.5:3b', 'starcoder2:3b'],
            'memory': '~3.6GB total',
            'pros': 'Good balance of quality and speed',
            'cons': 'None significant'
        }
    ]

    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec['scenario']}")
        print(f"   Models: {', '.join(rec['models'])}")
        print(f"   Memory: {rec['memory']}")
        print(f"   Pros: {rec['pros']}")
        print(f"   Cons: {rec['cons']}")
        print()


def optimize_ollama_settings():
    """Provide commands to optimize Ollama for 4GB GPU."""
    print("\n⚙️  Ollama Optimization Commands")
    print("=" * 40)

    print("1. Force GPU-only mode (no CPU/GPU split):")
    print("   set OLLAMA_GPU_LAYERS=999")
    print("   set OLLAMA_GPU_MEMORY_FRACTION=0.95")
    print()

    print("2. Optimize for 4GB GPU:")
    print("   set OLLAMA_MAX_LOADED_MODELS=2")
    print("   set OLLAMA_KEEP_ALIVE=5m")
    print()

    print("3. Restart Ollama with optimized settings:")
    print("   ollama serve")
    print()

    print("4. Test with a specific model:")
    print("   ollama run llama3.2:3b \"Test GPU optimization\"")
    print()


def create_optimization_script():
    """Create a batch script to apply optimizations."""
    script_content = """@echo off
echo Optimizing Ollama for 4GB Quadro P1000...

REM Set environment variables for GPU optimization
set OLLAMA_GPU_LAYERS=999
set OLLAMA_GPU_MEMORY_FRACTION=0.95
set OLLAMA_MAX_LOADED_MODELS=2
set OLLAMA_KEEP_ALIVE=5m
set OLLAMA_FLASH_ATTENTION=1

echo Environment variables set:
echo OLLAMA_GPU_LAYERS=%OLLAMA_GPU_LAYERS%
echo OLLAMA_GPU_MEMORY_FRACTION=%OLLAMA_GPU_MEMORY_FRACTION%
echo OLLAMA_MAX_LOADED_MODELS=%OLLAMA_MAX_LOADED_MODELS%
echo OLLAMA_KEEP_ALIVE=%OLLAMA_KEEP_ALIVE%
echo OLLAMA_FLASH_ATTENTION=%OLLAMA_FLASH_ATTENTION%

echo.
echo Stopping Ollama service...
taskkill /f /im ollama.exe 2>nul

echo Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo Starting Ollama with optimized settings...
start "" ollama serve

echo.
echo Optimization complete!
echo Models should now use 100%% GPU instead of CPU/GPU split.
echo.
pause
"""

    with open('optimize_ollama_gpu.bat', 'w') as f:
        f.write(script_content)

    print("✅ Created 'optimize_ollama_gpu.bat'")
    print("   Run this script to apply GPU optimizations")


def main():
    """Main analysis and optimization function."""
    print("🚀 Ollama GPU Memory Optimizer")
    print("Analyzing your 4GB Quadro P1000 setup...")
    print()

    # Analyze current state
    analysis = analyze_memory_usage()

    # Provide recommendations
    recommend_optimal_models()

    # Show optimization commands
    optimize_ollama_settings()

    # Create optimization script
    create_optimization_script()

    print("\n🎯 Summary for Your 4GB GPU:")
    print("-" * 30)

    if analysis['cpu_gpu_split']:
        print("❌ Issue: Models are split between CPU/GPU")
        print("💡 Solution: Set OLLAMA_GPU_LAYERS=999 to force 100% GPU")

    if analysis['gpu_used'] and analysis['gpu_total']:
        usage_percent = analysis['gpu_used'] / analysis['gpu_total'] * 100
        if usage_percent < 80:
            print(f"📈 Opportunity: Only using {usage_percent:.1f}% of GPU")
            print("💡 Solution: Load multiple models or use larger models")

    print("\n✅ Next Steps:")
    print("1. Run 'optimize_ollama_gpu.bat'")
    print("2. Wait for Ollama to restart")
    print("3. Test with: ollama run llama3.2:3b")
    print("4. Check GPU usage with: nvidia-smi")


if __name__ == "__main__":
    main()
