#!/usr/bin/env python3
"""
Test Audit Trail System for Phase B1 Enhancements
Validates comprehensive audit logging and tracking functionality.
"""

import asyncio
import json
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import our models and services
from src.ai_coding_agent.models import (
    Base, Project, Task, TaskStatus, AgentType,
    AuditLog, StatusHistory, ConcurrencyControl,
    AuditAction, AuditEntityType,
    ProjectCreate, RoadmapCreate, PhaseCreate, StepCreate, TaskCreate
)
from src.ai_coding_agent.services.roadmap import RoadmapService
from src.ai_coding_agent.services.audit import AuditService


def test_audit_trail_system():
    """Test the complete audit trail system."""
    print("🔍 Testing Phase B1 Audit Trail System...")

    # Create test database
    engine = create_engine("sqlite:///./test_audit.db", echo=False)
    Base.metadata.drop_all(engine)
    Base.metadata.create_all(engine)

    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Test user context
        test_user_id = "test-user-123"
        test_user_email = "<EMAIL>"

        # Initialize services
        roadmap_service = RoadmapService(db, user_id=test_user_id, user_email=test_user_email)
        audit_service = AuditService(db)

        print("\n1. Creating test project with roadmap...")

        # Create a test project with roadmap
        project_data = ProjectCreate(
            name="Audit Test Project",
            description="Testing audit trail functionality",
            tech_stack={"frontend": "React", "backend": "FastAPI"},
            roadmap=RoadmapCreate(
                name="Test Roadmap",
                version="1.0.0",
                phases=[
                    PhaseCreate(
                        name="Foundation",
                        description="Basic setup",
                        order_index=0,
                        steps=[
                            StepCreate(
                                name="Environment Setup",
                                description="Set up development environment",
                                order_index=0,
                                tasks=[
                                    TaskCreate(
                                        name="Initialize Repository",
                                        description="Create Git repo",
                                        order_index=0,
                                        assigned_agent=AgentType.SHELL,
                                        estimated_duration="30 minutes"
                                    ),
                                    TaskCreate(
                                        name="Setup Dependencies",
                                        description="Install project dependencies",
                                        order_index=1,
                                        assigned_agent=AgentType.BACKEND,
                                        estimated_duration="1 hour"
                                    )
                                ]
                            )
                        ]
                    )
                ]
            )
        )

        project = roadmap_service.create_project(project_data)
        print(f"✅ Created project: {project.id}")

        # Get the first task for testing
        first_task = project.roadmap.phases[0].steps[0].tasks[0]
        task_id = first_task.id
        print(f"✅ Found test task: {task_id}")

        print("\n2. Testing status changes with audit logging...")

        # Test status change 1: PENDING -> IN_PROGRESS
        print("   📝 Changing status: PENDING -> IN_PROGRESS")
        updated_task = roadmap_service.update_task_status(
            task_id=task_id,
            status=TaskStatus.IN_PROGRESS,
            reason="Starting work on repository initialization"
        )
        print(f"   ✅ Task status updated to: {updated_task.status}")

        # Test status change 2: IN_PROGRESS -> COMPLETED
        print("   📝 Changing status: IN_PROGRESS -> COMPLETED")
        updated_task = roadmap_service.update_task_status(
            task_id=task_id,
            status=TaskStatus.COMPLETED,
            artifacts=[{
                "type": "code",
                "filename": ".gitignore",
                "content": "node_modules/\n*.log",
                "description": "Git ignore file"
            }],
            reason="Repository successfully initialized with .gitignore"
        )
        print(f"   ✅ Task status updated to: {updated_task.status}")

        print("\n3. Testing audit log retrieval...")

        # Get audit trail for the task
        audit_trail = audit_service.get_entity_audit_trail(
            entity_type=AuditEntityType.TASK,
            entity_id=task_id,
            limit=10
        )

        print(f"   📊 Found {len(audit_trail)} audit log entries:")
        for i, log in enumerate(audit_trail, 1):
            print(f"      {i}. {log.action} by {log.user_id or 'system'} at {log.created_at}")
            if log.changed_fields:
                print(f"         Changed fields: {', '.join(log.changed_fields)}")
            if log.reason:
                print(f"         Reason: {log.reason}")

        print("\n4. Testing status history...")

        # Get status history for the task
        status_history = audit_service.get_status_history(
            entity_type=AuditEntityType.TASK,
            entity_id=task_id,
            limit=10
        )

        print(f"   📈 Found {len(status_history)} status history entries:")
        for i, history in enumerate(status_history, 1):
            duration_str = ""
            if history.duration_in_previous_status:
                duration_str = f" (spent {history.duration_in_previous_status}s in previous status)"
            print(f"      {i}. {history.old_status or 'initial'} -> {history.new_status}{duration_str}")
            if history.reason:
                print(f"         Reason: {history.reason}")

        print("\n5. Testing concurrency control...")

        # Test lock acquisition
        print("   🔒 Testing lock acquisition...")
        success, control = audit_service.acquire_lock(
            entity_type=AuditEntityType.TASK,
            entity_id=task_id,
            user_id=test_user_id,
            lock_duration_minutes=5
        )

        if success:
            print(f"   ✅ Lock acquired successfully. Version: {control.version}")
            print(f"      Locked by: {control.locked_by}")
            print(f"      Expires at: {control.lock_expires_at}")
        else:
            print("   ❌ Failed to acquire lock")

        # Test lock release
        print("   🔓 Testing lock release...")
        released_control = audit_service.release_lock(
            entity_type=AuditEntityType.TASK,
            entity_id=task_id,
            user_id=test_user_id
        )
        print(f"   ✅ Lock released. Is locked: {released_control.is_locked}")

        # Test version increment
        print("   📈 Testing version increment...")
        incremented_control = audit_service.increment_version(
            entity_type=AuditEntityType.TASK,
            entity_id=task_id,
            user_id=test_user_id
        )
        print(f"   ✅ Version incremented to: {incremented_control.version}")

        print("\n6. Testing audit log filtering...")

        # Test filtering by user
        from src.ai_coding_agent.models import AuditLogFilter
        user_logs = audit_service.get_audit_logs(
            filters=AuditLogFilter(user_id=test_user_id, limit=50)
        )
        print(f"   👤 Found {len(user_logs)} logs for user {test_user_id}")

        # Test filtering by entity type
        task_logs = audit_service.get_audit_logs(
            filters=AuditLogFilter(entity_type=AuditEntityType.TASK, limit=50)
        )
        print(f"   📋 Found {len(task_logs)} logs for tasks")

        # Test filtering by action
        status_change_logs = audit_service.get_audit_logs(
            filters=AuditLogFilter(action=AuditAction.STATUS_CHANGE, limit=50)
        )
        print(f"   🔄 Found {len(status_change_logs)} status change logs")

        print("\n7. Testing status analytics...")

        # Test status analytics
        analytics = audit_service.get_status_analytics(
            entity_type=AuditEntityType.TASK,
            entity_id=task_id
        )
        print(f"   📊 Status analytics for task:")
        print(f"      - Total status changes: {analytics['total_status_changes']}")
        print(f"      - Current status: {analytics['current_status']}")
        print(f"      - Status distribution: {analytics['status_distribution']}")
        print(f"      - Total time tracked: {analytics['total_time_tracked']}s")

        print("\n8. Testing versioning system...")

        # Test roadmap versioning
        from src.ai_coding_agent.services.versioning import VersioningService
        from src.ai_coding_agent.models import RoadmapVersionCreate

        versioning_service = VersioningService(db, user_id=test_user_id, user_email="<EMAIL>")

        # Create a version
        version_data = RoadmapVersionCreate(
            version_type="minor",
            change_summary="Added initial tasks and completed first task",
            creation_reason="Testing versioning system"
        )

        version = versioning_service.create_version(project.roadmap.id, version_data)
        print(f"   📦 Created version: {version.version_number}")
        print(f"      - Version type: {version.version_type}")
        print(f"      - Change summary: {version.change_summary}")

        # Get all versions
        versions = versioning_service.get_versions(project.roadmap.id)
        print(f"   📚 Found {len(versions)} versions for roadmap")

        # Release the version
        released_version = versioning_service.release_version(
            version.id,
            release_notes="Initial release with basic task structure"
        )
        print(f"   🚀 Released version {released_version.version_number}")
        print(f"      - Released at: {released_version.released_at}")
        print(f"      - Release notes: {released_version.release_notes}")

        print("\n✅ All audit trail tests completed successfully!")

        # Summary statistics
        print(f"\n📊 Summary:")
        print(f"   - Total audit logs: {len(audit_trail)}")
        print(f"   - Status changes tracked: {len(status_history)}")
        print(f"   - Concurrency control version: {incremented_control.version}")
        print(f"   - User-specific logs: {len(user_logs)}")
        print(f"   - Status analytics tracked: {analytics['total_status_changes']} changes")
        print(f"   - Roadmap versions created: {len(versions)}")
        print(f"   - Latest version: {released_version.version_number} (released)")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        db.close()


if __name__ == "__main__":
    success = test_audit_trail_system()
    if success:
        print("\n🎉 Phase B1 Audit Trail System implementation is working correctly!")
    else:
        print("\n💥 Phase B1 Audit Trail System has issues that need to be addressed.")
