/**
 * Collaboration Edge Component for ReactFlow
 *
 * Displays collaboration connections between agents
 */

import React from 'react';
import { EdgeProps, getBezierPath } from '@xyflow/react';
import { CollaborationEvent } from '../../types/agents';

interface CollaborationEdgeData {
  event: CollaborationEvent;
  isActive: boolean;
}

const CollaborationEdgeComponent: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
}) => {
  const edgeData = data as CollaborationEdgeData;
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const isActive = edgeData?.isActive || false;
  const event = edgeData?.event;

  return (
    <>
      <path
        id={id}
        style={{
          ...style,
          strokeWidth: isActive ? 3 : 1,
          stroke: isActive ? '#3b82f6' : '#94a3b8',
          strokeDasharray: isActive ? '5,5' : undefined,
        }}
        className={isActive ? 'animate-pulse' : ''}
        d={edgePath}
        markerEnd={markerEnd}
      />

      {/* Edge Label */}
      {event && (
        <foreignObject
          width={120}
          height={40}
          x={labelX - 60}
          y={labelY - 20}
          className="overflow-visible"
          requiredExtensions="http://www.w3.org/1999/xhtml"
        >
          <div className="flex items-center justify-center h-full">
            <div
              className={`
                px-2 py-1 rounded text-xs font-medium shadow-sm border
                ${isActive
                  ? 'bg-blue-100 text-blue-800 border-blue-200'
                  : 'bg-gray-100 text-gray-600 border-gray-200'
                }
              `}
            >
              {event.type.replace('_', ' ')}
            </div>
          </div>
        </foreignObject>
      )}

      {/* Active Flow Animation */}
      {isActive && (
        <circle r="3" fill="#3b82f6" className="opacity-75">
          <animateMotion dur="2s" repeatCount="indefinite" path={edgePath} />
        </circle>
      )}
    </>
  );
};

export default CollaborationEdgeComponent;
