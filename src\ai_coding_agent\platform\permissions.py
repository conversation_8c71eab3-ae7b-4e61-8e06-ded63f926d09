"""
Platform-specific permission management for secure workspace isolation.

This module provides permission handling functionality for Windows (ACL),
macOS, and Linux systems with proper security enforcement.
"""

import os
import stat
import subprocess
from pathlib import Path
from typing import Optional, List
import logging

from .detector import get_platform_info, SupportedPlatform

logger = logging.getLogger(__name__)


class PlatformPermissions:
    """Platform-specific permission management."""

    def __init__(self):
        self.platform_info = get_platform_info()

    def set_directory_permissions(self, directory: Path, owner_only: bool = True) -> bool:
        """
        Set appropriate permissions for a directory based on the platform.

        Args:
            directory: Directory path to set permissions for
            owner_only: Whether to restrict access to owner only (default: True)

        Returns:
            bool: True if permissions were set successfully
        """
        try:
            if not directory.exists():
                logger.error(f"Directory does not exist: {directory}")
                return False

            platform_type = self.platform_info.platform_type

            if platform_type == SupportedPlatform.WINDOWS:
                return self._set_windows_permissions(directory, owner_only)
            elif platform_type in [SupportedPlatform.MACOS, SupportedPlatform.LINUX]:
                return self._set_unix_permissions(directory, owner_only)
            else:
                logger.error(f"Unsupported platform for permissions: {platform_type}")
                return False

        except Exception as e:
            logger.error(f"Failed to set directory permissions: {e}")
            return False

    def _set_windows_permissions(self, directory: Path, owner_only: bool) -> bool:
        """Set Windows-specific permissions using ACL."""
        try:
            # Convert to absolute path for Windows commands
            abs_path = directory.resolve()

            if owner_only:
                # Remove inheritance and set owner-only permissions
                commands = [
                    # Disable inheritance
                    ['icacls', str(abs_path), '/inheritance:d'],
                    # Remove all users except owner
                    ['icacls', str(abs_path), '/remove:g', 'Users'],
                    ['icacls', str(abs_path), '/remove:g', 'Everyone'],
                    # Grant full control to owner
                    ['icacls', str(abs_path), '/grant:r', f'{os.environ.get("USERNAME", "Unknown")}:(OI)(CI)F']
                ]

                for cmd in commands:
                    try:
                        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
                        if result.returncode != 0:
                            logger.warning(f"Windows permission command failed: {' '.join(cmd)}")
                            logger.warning(f"Error: {result.stderr}")
                    except FileNotFoundError:
                        logger.warning("icacls command not found, using basic permissions")
                        # Fallback to basic permissions
                        os.chmod(directory, stat.S_IRWXU)
                        return True

            logger.info(f"Windows permissions set for: {directory}")
            return True

        except Exception as e:
            logger.error(f"Windows permission setting failed: {e}")
            return False

    def _set_unix_permissions(self, directory: Path, owner_only: bool) -> bool:
        """Set Unix-like permissions (macOS/Linux)."""
        try:
            if owner_only:
                # Set permissions to 700 (owner read/write/execute only)
                os.chmod(directory, stat.S_IRWXU)

                # Recursively set permissions for all subdirectories and files
                for root, dirs, files in os.walk(directory):
                    # Set directory permissions
                    for dir_name in dirs:
                        dir_path = Path(root) / dir_name
                        os.chmod(dir_path, stat.S_IRWXU)

                    # Set file permissions
                    for file_name in files:
                        file_path = Path(root) / file_name
                        os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR)
            else:
                # Set more permissive permissions (755 for directories, 644 for files)
                os.chmod(directory, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)

            logger.info(f"Unix permissions set for: {directory}")
            return True

        except Exception as e:
            logger.error(f"Unix permission setting failed: {e}")
            return False

    def verify_permissions(self, directory: Path) -> bool:
        """
        Verify that directory permissions are set correctly.

        Args:
            directory: Directory to verify permissions for

        Returns:
            bool: True if permissions are correct
        """
        try:
            if not directory.exists():
                return False

            platform_type = self.platform_info.platform_type

            if platform_type == SupportedPlatform.WINDOWS:
                return self._verify_windows_permissions(directory)
            elif platform_type in [SupportedPlatform.MACOS, SupportedPlatform.LINUX]:
                return self._verify_unix_permissions(directory)
            else:
                logger.error(f"Unsupported platform for permission verification: {platform_type}")
                return False

        except Exception as e:
            logger.error(f"Permission verification failed: {e}")
            return False

    def _verify_windows_permissions(self, directory: Path) -> bool:
        """Verify Windows permissions."""
        try:
            # Check if directory is accessible by current user
            if not os.access(directory, os.R_OK | os.W_OK):
                logger.error(f"Directory not accessible: {directory}")
                return False

            # Additional Windows-specific checks could be added here
            logger.debug(f"Windows permissions verified for: {directory}")
            return True

        except Exception as e:
            logger.error(f"Windows permission verification failed: {e}")
            return False

    def _verify_unix_permissions(self, directory: Path) -> bool:
        """Verify Unix permissions."""
        try:
            # Get directory permissions
            dir_stat = directory.stat()
            dir_mode = stat.filemode(dir_stat.st_mode)

            # Check if permissions are owner-only (700)
            expected_perms = stat.S_IRWXU
            actual_perms = dir_stat.st_mode & 0o777

            if actual_perms != expected_perms:
                logger.warning(f"Unexpected permissions for {directory}: {oct(actual_perms)} (expected: {oct(expected_perms)})")
                return False

            logger.debug(f"Unix permissions verified for: {directory} ({dir_mode})")
            return True

        except Exception as e:
            logger.error(f"Unix permission verification failed: {e}")
            return False


# Global permissions manager instance
_permissions_manager = PlatformPermissions()


def set_platform_permissions(directory: Path, owner_only: bool = True) -> bool:
    """
    Set platform-appropriate permissions for a directory.

    Args:
        directory: Directory to set permissions for
        owner_only: Whether to restrict access to owner only

    Returns:
        bool: True if permissions were set successfully
    """
    return _permissions_manager.set_directory_permissions(directory, owner_only)
