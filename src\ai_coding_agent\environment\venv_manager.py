"""
Virtual environment management for cross-platform project isolation.

This module provides comprehensive virtual environment creation, management,
and validation functionality across Windows, macOS, and Linux platforms.
"""

import os
import sys
import subprocess
import venv
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass
import logging

from ..platform import get_platform_info, SupportedPlatform
from ..workspace import get_workspace_manager

logger = logging.getLogger(__name__)


@dataclass
class VenvInfo:
    """Virtual environment information."""
    name: str
    path: Path
    python_executable: Path
    pip_executable: Path
    python_version: str
    is_active: bool = False

    def __post_init__(self):
        """Ensure paths are Path objects."""
        if isinstance(self.path, str):
            self.path = Path(self.path)
        if isinstance(self.python_executable, str):
            self.python_executable = Path(self.python_executable)
        if isinstance(self.pip_executable, str):
            self.pip_executable = Path(self.pip_executable)


class VenvManager:
    """Cross-platform virtual environment manager."""

    def __init__(self):
        self.platform_info = get_platform_info()
        self.workspace_manager = get_workspace_manager()

    def create_project_venv(self, project_name: str, python_version: Optional[str] = None) -> VenvInfo:
        """
        Create a virtual environment for a specific project.

        Args:
            project_name: Name of the project
            python_version: Specific Python version to use (e.g., "3.11")

        Returns:
            VenvInfo: Information about the created virtual environment

        Raises:
            ValueError: If project doesn't exist
            RuntimeError: If venv creation fails
        """
        try:
            # Get project path
            project_path = self.workspace_manager.get_project_path(project_name, active=True)
            if not project_path or not project_path.exists():
                raise ValueError(f"Project not found: {project_name}")

            # Create venv directory within project
            venv_path = project_path / "venv"
            if venv_path.exists():
                logger.warning(f"Virtual environment already exists for project: {project_name}")
                return self._get_venv_info(project_name, venv_path)

            # Determine Python executable
            python_executable = self._find_python_executable(python_version)

            # Create virtual environment
            self._create_venv(venv_path, python_executable)

            # Get venv info
            venv_info = self._get_venv_info(project_name, venv_path)

            # Upgrade pip
            self._upgrade_pip(venv_info)

            logger.info(f"Created virtual environment for project: {project_name}")
            return venv_info

        except Exception as e:
            logger.error(f"Failed to create venv for project {project_name}: {e}")
            raise RuntimeError(f"Virtual environment creation failed: {e}")

    def _find_python_executable(self, version: Optional[str] = None) -> str:
        """Find appropriate Python executable."""
        if version:
            # Try to find specific version
            possible_names = [
                f"python{version}",
                f"python{version.replace('.', '')}",
                f"py -{version}",  # Windows py launcher
            ]

            for name in possible_names:
                try:
                    result = subprocess.run([name, "--version"],
                                          capture_output=True, text=True, check=True)
                    if version in result.stdout:
                        logger.debug(f"Found Python {version}: {name}")
                        return name
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue

            logger.warning(f"Python {version} not found, using default")

        # Use current Python executable
        return sys.executable

    def _create_venv(self, venv_path: Path, python_executable: str) -> None:
        """Create virtual environment using the specified Python executable."""
        try:
            # Use venv module for creation
            if python_executable == sys.executable:
                # Use built-in venv module
                venv.create(venv_path, with_pip=True, clear=True)
            else:
                # Use external Python executable
                cmd = [python_executable, "-m", "venv", str(venv_path), "--clear"]
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                logger.debug(f"Venv creation output: {result.stdout}")

            logger.debug(f"Virtual environment created at: {venv_path}")

        except subprocess.CalledProcessError as e:
            logger.error(f"Venv creation command failed: {e.stderr}")
            raise RuntimeError(f"Failed to create virtual environment: {e.stderr}")
        except Exception as e:
            logger.error(f"Venv creation failed: {e}")
            raise RuntimeError(f"Virtual environment creation error: {e}")

    def _get_venv_info(self, project_name: str, venv_path: Path) -> VenvInfo:
        """Get information about a virtual environment."""
        try:
            platform_type = self.platform_info.platform_type

            if platform_type == SupportedPlatform.WINDOWS:
                python_exe = venv_path / "Scripts" / "python.exe"
                pip_exe = venv_path / "Scripts" / "pip.exe"
            else:  # macOS/Linux
                python_exe = venv_path / "bin" / "python"
                pip_exe = venv_path / "bin" / "pip"

            # Verify executables exist
            if not python_exe.exists():
                raise RuntimeError(f"Python executable not found: {python_exe}")
            if not pip_exe.exists():
                raise RuntimeError(f"Pip executable not found: {pip_exe}")

            # Get Python version
            try:
                result = subprocess.run([str(python_exe), "--version"],
                                      capture_output=True, text=True, check=True)
                python_version = result.stdout.strip()
            except subprocess.CalledProcessError:
                python_version = "Unknown"

            return VenvInfo(
                name=project_name,
                path=venv_path,
                python_executable=python_exe,
                pip_executable=pip_exe,
                python_version=python_version
            )

        except Exception as e:
            logger.error(f"Failed to get venv info: {e}")
            raise RuntimeError(f"Virtual environment info retrieval failed: {e}")

    def _upgrade_pip(self, venv_info: VenvInfo) -> None:
        """Upgrade pip in the virtual environment."""
        try:
            cmd = [str(venv_info.pip_executable), "install", "--upgrade", "pip"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.debug(f"Pip upgrade output: {result.stdout}")

        except subprocess.CalledProcessError as e:
            logger.warning(f"Failed to upgrade pip: {e.stderr}")
        except Exception as e:
            logger.warning(f"Pip upgrade error: {e}")

    def install_packages(self, venv_info: VenvInfo, packages: List[str]) -> bool:
        """
        Install packages in the virtual environment.

        Args:
            venv_info: Virtual environment information
            packages: List of package names to install

        Returns:
            bool: True if installation was successful
        """
        try:
            if not packages:
                return True

            cmd = [str(venv_info.pip_executable), "install"] + packages
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            logger.info(f"Installed packages in {venv_info.name}: {', '.join(packages)}")
            logger.debug(f"Installation output: {result.stdout}")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Package installation failed: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"Package installation error: {e}")
            return False

    def list_packages(self, venv_info: VenvInfo) -> List[str]:
        """
        List installed packages in the virtual environment.

        Args:
            venv_info: Virtual environment information

        Returns:
            List[str]: List of installed package names
        """
        try:
            cmd = [str(venv_info.pip_executable), "list", "--format=freeze"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            packages = []
            for line in result.stdout.strip().split('\n'):
                if line and '==' in line:
                    package_name = line.split('==')[0]
                    packages.append(package_name)

            logger.debug(f"Found {len(packages)} packages in {venv_info.name}")
            return packages

        except subprocess.CalledProcessError as e:
            logger.error(f"Package listing failed: {e.stderr}")
            return []
        except Exception as e:
            logger.error(f"Package listing error: {e}")
            return []

    def remove_venv(self, project_name: str) -> bool:
        """
        Remove virtual environment for a project.

        Args:
            project_name: Name of the project

        Returns:
            bool: True if removal was successful
        """
        try:
            project_path = self.workspace_manager.get_project_path(project_name, active=True)
            if not project_path:
                logger.warning(f"Project not found: {project_name}")
                return False

            venv_path = project_path / "venv"
            if not venv_path.exists():
                logger.warning(f"Virtual environment not found for project: {project_name}")
                return True

            # Remove venv directory
            import shutil
            shutil.rmtree(venv_path)

            logger.info(f"Removed virtual environment for project: {project_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to remove venv for project {project_name}: {e}")
            return False

    def get_venv_info(self, project_name: str) -> Optional[VenvInfo]:
        """
        Get virtual environment information for a project.

        Args:
            project_name: Name of the project

        Returns:
            Optional[VenvInfo]: Virtual environment info if it exists
        """
        try:
            project_path = self.workspace_manager.get_project_path(project_name, active=True)
            if not project_path:
                return None

            venv_path = project_path / "venv"
            if not venv_path.exists():
                return None

            return self._get_venv_info(project_name, venv_path)

        except Exception as e:
            logger.error(f"Failed to get venv info for project {project_name}: {e}")
            return None


# Global venv manager instance
_venv_manager: Optional[VenvManager] = None


def get_venv_manager() -> VenvManager:
    """Get the global virtual environment manager."""
    global _venv_manager
    if _venv_manager is None:
        _venv_manager = VenvManager()
    return _venv_manager


def create_project_venv(project_name: str, python_version: Optional[str] = None) -> VenvInfo:
    """
    Create a virtual environment for a project using the global manager.

    Args:
        project_name: Name of the project
        python_version: Specific Python version to use

    Returns:
        VenvInfo: Information about the created virtual environment
    """
    return get_venv_manager().create_project_venv(project_name, python_version)
