"""
Model Performance Comparison and Recommendation

Compare current mistral:7b-instruct-q4_0 with available alternatives
for the Architect Agent role.
"""

import asyncio
import time
import json
import subprocess
import sys
import os
from typing import Dict, List, Any, Optional

# Available models from your ollama list
AVAILABLE_MODELS = [
    "mistral:7b-instruct-q4_0",  # Current
    "mistral:latest",            # Alternative
    "llama3.2:3b",              # Smaller, faster
    "gemma3:4b",                # Medium size
    "qwen3:8b",                 # Larger but newer
    "starcoder2:3b",            # Advanced code generation
    "yi-coder:1.5b",            # Fast code completion
]

# Test prompts for Architect Agent tasks
ARCHITECT_TESTS = [
    {
        "name": "Project Planning",
        "prompt": "Create a high-level roadmap for building a simple todo app with React frontend and FastAPI backend."
    },
    {
        "name": "Requirements Analysis",
        "prompt": "Break down this user request into specific tasks: 'I want a user authentication system with social login'."
    },
    {
        "name": "Agent Coordination",
        "prompt": "How would you coordinate Frontend, Backend, and Database agents to implement a comment system?"
    },
    {
        "name": "Architecture Decision",
        "prompt": "Should we use SQLite or PostgreSQL for a small startup's <PERSON>? Explain the tradeoffs."
    }
]

def safe_subprocess_run(cmd: List[str], timeout: int = 90) -> Dict[str, Any]:
    """
    Safely run subprocess with proper Unicode handling for Windows.

    Args:
        cmd: Command to run as list of strings
        timeout: Timeout in seconds

    Returns:
        Dict with success, stdout, stderr, returncode
    """
    try:
        # Set up environment for UTF-8 handling
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        # On Windows, ensure we handle Unicode properly
        if sys.platform == "win32":
            # Use UTF-8 and replace errors instead of failing
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                timeout=timeout,
                env=env,
                shell=False  # More secure
            )
        else:
            # On Unix systems, UTF-8 is usually default
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                env=env
            )

        # Clean output of control characters
        def clean_text(text: str) -> str:
            if not text:
                return ""
            # Keep only printable characters and basic whitespace
            return ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')

        return {
            "success": result.returncode == 0,
            "stdout": clean_text(result.stdout),
            "stderr": clean_text(result.stderr),
            "returncode": result.returncode
        }

    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"Command timed out after {timeout} seconds",
            "returncode": -1
        }
    except UnicodeDecodeError as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"Unicode decode error: {str(e)}",
            "returncode": -2
        }
    except FileNotFoundError:
        return {
            "success": False,
            "stdout": "",
            "stderr": "Command not found. Ensure Ollama is installed and in PATH.",
            "returncode": -3
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"Unexpected error: {str(e)}",
            "returncode": -4
        }

def check_ollama_availability() -> bool:
    """Check if Ollama is available and responding."""
    result = safe_subprocess_run(["ollama", "list"], timeout=10)
    return result["success"]

async def test_model_performance(model: str, test_prompt: str) -> Dict[str, Any]:
    """Test a single model with a single prompt with robust Unicode handling."""
    start_time = time.time()

    try:
        cmd = [
            "ollama", "run", model,
            f"You are an AI Architect Agent. {test_prompt} Keep response under 200 words."
        ]

        # Use the safe subprocess runner with extended timeout
        result = safe_subprocess_run(cmd, timeout=90)
        end_time = time.time()
        response_time = end_time - start_time

        if result["success"]:
            response = result["stdout"].strip()
            word_count = len(response.split()) if response else 0

            return {
                "success": True,
                "response_time": response_time,
                "word_count": word_count,
                "words_per_second": word_count / response_time if response_time > 0 else 0,
                "response": response[:100] + "..." if len(response) > 100 else response
            }
        else:
            return {
                "success": False,
                "error": result["stderr"][:200] if result["stderr"] else "Unknown error",
                "response_time": response_time
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)[:100]}",
            "response_time": time.time() - start_time
        }

async def compare_models():
    """Compare all available models for Architect Agent tasks."""
    print("🔍 Model Performance Comparison for Architect Agent")
    print("=" * 70)

    # Check if Ollama is available
    print("🔧 Checking Ollama availability...", end=" ")
    if not check_ollama_availability():
        print("❌ FAILED")
        print("\n🚨 ERROR: Ollama is not available or not responding.")
        print("Please ensure:")
        print("- Ollama is installed and running")
        print("- Ollama is accessible from command line")
        print("- Try running 'ollama list' manually")
        return None, {}
    print("✅ OK")

    results = {}

    for model in AVAILABLE_MODELS:
        print(f"\n🤖 Testing {model}...")
        model_results = []

        for test in ARCHITECT_TESTS:
            print(f"  📝 {test['name']}...", end=" ", flush=True)
            result = await test_model_performance(model, test["prompt"])
            model_results.append({
                "test_name": test["name"],
                **result
            })

            if result["success"]:
                print(f"✅ {result['response_time']:.1f}s ({result['words_per_second']:.1f} w/s)")
            else:
                print(f"❌ {result.get('error', 'Failed')[:50]}")

        results[model] = model_results

    # Analysis
    print("\n📊 PERFORMANCE SUMMARY")
    print("=" * 70)

    model_scores = {}

    for model, tests in results.items():
        successful_tests = [t for t in tests if t["success"]]

        if successful_tests:
            avg_time = sum(t["response_time"] for t in successful_tests) / len(successful_tests)
            avg_wps = sum(t["words_per_second"] for t in successful_tests) / len(successful_tests)
            success_rate = len(successful_tests) / len(tests)

            # Calculate composite score (lower is better for time, higher for others)
            score = (success_rate * 100) + (avg_wps * 2) - (avg_time * 5)

            model_scores[model] = {
                "avg_response_time": avg_time,
                "avg_words_per_second": avg_wps,
                "success_rate": success_rate,
                "score": score
            }

            print(f"{model:25} | {avg_time:5.1f}s | {avg_wps:6.1f} w/s | {success_rate:5.1%} | Score: {score:6.1f}")
        else:
            print(f"{model:25} | FAILED ALL TESTS")
            model_scores[model] = {"score": -1000}

    # Recommendation
    print("\n🏆 RECOMMENDATION")
    print("=" * 70)

    best_model = max(model_scores.items(), key=lambda x: x[1]["score"])
    current_model = "mistral:7b-instruct-q4_0"

    print(f"Best performing model: {best_model[0]}")
    print(f"Current model: {current_model}")

    if best_model[0] != current_model:
        current_score = model_scores.get(current_model, {}).get("score", -1000)
        improvement = best_model[1]["score"] - current_score
        print(f"Switching would improve performance by {improvement:.1f} points")

        # Specific recommendations
        print(f"\n💡 ANALYSIS:")

        if "llama3.2:3b" in model_scores and model_scores["llama3.2:3b"]["score"] > current_score:
            print("- llama3.2:3b (3B params) might be faster due to smaller size")

        if "gemma3:4b" in model_scores and model_scores["gemma3:4b"]["score"] > current_score:
            print("- gemma3:4b (4B params) offers good balance of speed and capability")

        if "qwen3:8b" in model_scores and model_scores["qwen3:8b"]["score"] > current_score:
            print("- qwen3:8b (8B params) might give better quality but slower speed")
    else:
        print("Current model is already optimal!")

    # GPU Memory Analysis
    print(f"\n🖥️  GPU MEMORY CONSIDERATIONS (4GB Quadro P1000):")
    model_sizes = {
        "mistral:7b-instruct-q4_0": "~4GB",
        "mistral:latest": "~4GB",
        "llama3.2:3b": "~2GB",
        "gemma3:4b": "~3GB",
        "qwen3:8b": "~5GB",
        "starcoder2:3b": "~2GB",
        "yi-coder:1.5b": "~1GB"
    }

    for model in AVAILABLE_MODELS:
        size = model_sizes.get(model, "Unknown")
        fit_status = "✅ Fits" if any(s in size for s in ["1GB", "2GB", "3GB", "4GB"]) else "⚠️  Tight fit" if "5GB" in size else "❓"
        print(f"  {model:25} | {size:8} | {fit_status}")

    return best_model[0], results

if __name__ == "__main__":
    print("🚀 Starting comprehensive model comparison...")
    print(f"🖥️  Platform: {sys.platform}")
    print(f"🔤 Default encoding: {sys.getdefaultencoding()}")
    print()

    try:
        best_model, detailed_results = asyncio.run(compare_models())

        if best_model and detailed_results:
            print(f"\n🎯 FINAL RECOMMENDATION: {best_model}")
            print("\nTo update your agent configuration, modify:")
            print("src/ai_coding_agent/config.py - architect_agent_model setting")
            print(".env - ARCHITECT_AGENT_MODEL variable")
        else:
            print("\n❌ Model comparison failed. Please check Ollama installation and try again.")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\n🛑 Comparison interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error during comparison: {str(e)}")
        print("Please check your Ollama installation and try again.")
        sys.exit(1)
