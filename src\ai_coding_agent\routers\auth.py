"""
Authentication router for user login, registration, and token management.

This module provides REST API endpoints for user authentication
following security best practices.
"""

from datetime import timed<PERSON><PERSON>
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from ..config import settings
from ..models import get_db, User, UserCreate, UserResponse, UserLogin
from ..services import auth, user as user_service

router = APIRouter()
security = HTTPBearer()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
) -> Any:
    """
    Register a new user account.

    Creates a new user with email verification and secure password hashing.

    Args:
        user_data: User registration data
        db: Database session

    Returns:
        UserResponse: The created user data (excluding password)

    Raises:
        HTTPException: If username or email already exists
    """
    try:
        db_user = user_service.create_user(db=db, user=user_data)
        return UserResponse.from_orm(db_user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {str(e)}"
        )


@router.post("/login")
async def login_user(
    user_credentials: UserLogin,
    db: Session = Depends(get_db)
) -> Any:
    """
    Authenticate user and return access tokens.

    Validates user credentials and returns JWT access and refresh tokens.

    Args:
        user_credentials: Username/email and password
        db: Database session

    Returns:
        dict: Access token, refresh token, and token type

    Raises:
        HTTPException: If credentials are invalid
    """
    authenticated_user = auth.authenticate_user(
        db, user_credentials.username, user_credentials.password
    )

    if not authenticated_user or authenticated_user is False:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Cast to User type for type safety
    if not isinstance(authenticated_user, User):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user: User = authenticated_user

    if not getattr(user, 'is_active', False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user account"
        )

    # Create tokens
    access_token_expires = timedelta(minutes=settings.security.access_token_expire_minutes)
    access_token = auth.create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    refresh_token = auth.create_refresh_token(data={"sub": user.username})

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.security.access_token_expire_minutes * 60,
        "user": UserResponse.from_orm(user)
    }


@router.post("/refresh")
async def refresh_token(
    refresh_token: str,
    db: Session = Depends(get_db)
) -> Any:
    """
    Refresh an access token using a refresh token.

    Args:
        refresh_token: Valid refresh token
        db: Database session

    Returns:
        dict: New access token and expiry

    Raises:
        HTTPException: If refresh token is invalid
    """
    payload = auth.verify_token(refresh_token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

    username = payload.get("sub")
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

    user = auth.get_user_by_username(db, username)
    if user is None or not getattr(user, 'is_active', False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )

    # Create new access token
    access_token_expires = timedelta(minutes=settings.security.access_token_expire_minutes)
    access_token = auth.create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.security.access_token_expire_minutes * 60
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(auth.get_current_active_user)
) -> Any:
    """
    Get current authenticated user information.

    Args:
        current_user: Current authenticated user

    Returns:
        UserResponse: Current user data
    """
    return UserResponse.from_orm(current_user)


@router.post("/logout")
async def logout_user(
    current_user: User = Depends(auth.get_current_active_user)
) -> Any:
    """
    Logout current user.

    Note: Since we're using stateless JWT tokens, this endpoint
    primarily serves as a confirmation. In a production environment,
    you might want to implement token blacklisting.

    Args:
        current_user: Current authenticated user

    Returns:
        dict: Logout confirmation message
    """
    return {"message": f"User {current_user.username} logged out successfully"}
