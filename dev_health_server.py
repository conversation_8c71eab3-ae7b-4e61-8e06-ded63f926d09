#!/usr/bin/env python3
"""
Temporary development server to provide health check endpoint
This allows the frontend to show "Online" status while the main backend is being configured
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import threading
import time

class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        # Enable CORS for frontend requests
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:3005')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, X-CSRF-Token')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        self.end_headers()

        if self.path == '/health':
            # Health check response
            response = {
                "status": "healthy",
                "service": "AI Coding Agent API",
                "timestamp": time.time(),
                "version": "1.0.0-dev"
            }
        elif self.path == '/':
            # Root endpoint
            response = {
                "message": "AI Coding Agent API - Development Server",
                "status": "running",
                "endpoints": ["/health"]
            }
        else:
            # 404 for other paths
            self.send_response(404)
            response = {"error": "Not found"}

        self.wfile.write(json.dumps(response).encode())

    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:3005')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, X-CSRF-Token')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        self.end_headers()

    def log_message(self, format, *args):
        # Suppress default logging
        pass

def run_server():
    server_address = ('localhost', 8001)
    httpd = HTTPServer(server_address, HealthCheckHandler)
    print(f"🚀 Development health server starting on http://localhost:8001")
    print(f"📡 Health endpoint: http://localhost:8001/health")
    print(f"🔄 Update frontend API_BASE_URL to http://localhost:8001")
    print(f"⏹️  Press Ctrl+C to stop")

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
