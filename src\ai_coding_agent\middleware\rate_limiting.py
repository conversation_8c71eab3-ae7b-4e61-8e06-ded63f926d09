"""
Rate limiting middleware for admin endpoints.

Provides protection against brute force attacks and API abuse
on administrative functions.
"""

import time
from typing import Dict, Optional
from collections import defaultdict, deque
from fastapi import HTT<PERSON>Exception, Request, status
from datetime import datetime, timedelta


class RateLimiter:
    """
    Simple in-memory rate limiter for admin endpoints.
    
    In production, consider using Redis or similar for distributed rate limiting.
    """
    
    def __init__(self):
        # Store request timestamps per IP
        self.requests: Dict[str, deque] = defaultdict(deque)
        # Store blocked IPs with expiry
        self.blocked_ips: Dict[str, datetime] = {}
    
    def is_allowed(
        self,
        ip_address: str,
        max_requests: int = 10,
        window_minutes: int = 5,
        block_duration_minutes: int = 15
    ) -> bool:
        """
        Check if request from IP is allowed based on rate limits.
        
        Args:
            ip_address: Client IP address
            max_requests: Maximum requests allowed in window
            window_minutes: Time window in minutes
            block_duration_minutes: How long to block after limit exceeded
            
        Returns:
            bool: True if request is allowed, False if rate limited
        """
        now = datetime.utcnow()
        
        # Check if IP is currently blocked
        if ip_address in self.blocked_ips:
            if now < self.blocked_ips[ip_address]:
                return False
            else:
                # Block expired, remove it
                del self.blocked_ips[ip_address]
        
        # Clean old requests outside the window
        window_start = now - timedelta(minutes=window_minutes)
        request_times = self.requests[ip_address]
        
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # Check if under rate limit
        if len(request_times) < max_requests:
            request_times.append(now)
            return True
        else:
            # Rate limit exceeded, block the IP
            self.blocked_ips[ip_address] = now + timedelta(minutes=block_duration_minutes)
            return False
    
    def get_remaining_requests(self, ip_address: str, max_requests: int = 10) -> int:
        """Get number of remaining requests for an IP."""
        current_requests = len(self.requests.get(ip_address, []))
        return max(0, max_requests - current_requests)
    
    def get_reset_time(self, ip_address: str, window_minutes: int = 5) -> Optional[datetime]:
        """Get when the rate limit window resets for an IP."""
        request_times = self.requests.get(ip_address)
        if not request_times:
            return None
        
        oldest_request = request_times[0]
        return oldest_request + timedelta(minutes=window_minutes)


# Global rate limiter instance
admin_rate_limiter = RateLimiter()


async def check_admin_rate_limit(request: Request):
    """
    FastAPI dependency to check rate limits for admin endpoints.
    
    Usage:
        @router.get("/admin/endpoint")
        async def admin_endpoint(
            _: None = Depends(check_admin_rate_limit),
            current_admin: User = Depends(get_current_admin_user)
        ):
            ...
    """
    client_ip = request.client.host if request.client else "unknown"
    
    # More restrictive limits for admin endpoints
    if not admin_rate_limiter.is_allowed(
        ip_address=client_ip,
        max_requests=20,  # 20 requests
        window_minutes=5,  # per 5 minutes
        block_duration_minutes=30  # block for 30 minutes if exceeded
    ):
        remaining = admin_rate_limiter.get_remaining_requests(client_ip, 20)
        reset_time = admin_rate_limiter.get_reset_time(client_ip, 5)
        
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error": "Rate limit exceeded for admin endpoints",
                "remaining_requests": remaining,
                "reset_time": reset_time.isoformat() if reset_time else None,
                "retry_after": 1800  # 30 minutes in seconds
            },
            headers={
                "Retry-After": "1800",
                "X-RateLimit-Limit": "20",
                "X-RateLimit-Remaining": str(remaining),
                "X-RateLimit-Reset": str(int(reset_time.timestamp())) if reset_time else ""
            }
        )


async def check_auth_rate_limit(request: Request):
    """
    More restrictive rate limiting for authentication endpoints.
    
    Helps prevent brute force attacks on login endpoints.
    """
    client_ip = request.client.host if request.client else "unknown"
    
    if not admin_rate_limiter.is_allowed(
        ip_address=client_ip,
        max_requests=5,  # Only 5 auth attempts
        window_minutes=15,  # per 15 minutes
        block_duration_minutes=60  # block for 1 hour if exceeded
    ):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error": "Too many authentication attempts",
                "message": "Please wait before trying again",
                "retry_after": 3600  # 1 hour
            },
            headers={"Retry-After": "3600"}
        )
