#!/usr/bin/env python3
"""
Database migration and versioning system.
Handles schema changes and data migrations across all database systems.
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict

@dataclass
class Migration:
    """Database migration definition."""
    id: str
    name: str
    description: str
    target_database: str  # 'local', 'supabase', 'vector', 'all'
    sql_up: Optional[str] = None
    sql_down: Optional[str] = None
    python_script: Optional[str] = None
    created_at: str = ""
    applied_at: Optional[str] = None

class MigrationManager:
    """Manage database migrations across hybrid architecture."""

    def __init__(self):
        self.project_root = Path.cwd()
        self.migrations_dir = self.project_root / "migrations"
        self.migrations_dir.mkdir(exist_ok=True)

        # Migration tracking files
        self.local_migrations_file = self.migrations_dir / "local_applied.json"
        self.supabase_migrations_file = self.migrations_dir / "supabase_applied.json"
        self.vector_migrations_file = self.migrations_dir / "vector_applied.json"

    def create_migration(
        self,
        name: str,
        description: str,
        target_database: str = "all"
    ) -> str:
        """Create a new migration file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        migration_id = f"{timestamp}_{name.lower().replace(' ', '_')}"

        migration = Migration(
            id=migration_id,
            name=name,
            description=description,
            target_database=target_database,
            created_at=datetime.now().isoformat()
        )

        # Create migration file
        migration_file = self.migrations_dir / f"{migration_id}.json"
        with open(migration_file, 'w') as f:
            json.dump(asdict(migration), f, indent=2)

        # Create SQL template files
        if target_database in ['local', 'all']:
            sql_up_file = self.migrations_dir / f"{migration_id}_local_up.sql"
            sql_down_file = self.migrations_dir / f"{migration_id}_local_down.sql"

            sql_up_file.write_text(f"-- Migration: {name}\n-- Target: Local Database\n-- Up migration\n\n")
            sql_down_file.write_text(f"-- Migration: {name}\n-- Target: Local Database\n-- Down migration\n\n")

        if target_database in ['supabase', 'all']:
            sql_up_file = self.migrations_dir / f"{migration_id}_supabase_up.sql"
            sql_down_file = self.migrations_dir / f"{migration_id}_supabase_down.sql"

            sql_up_file.write_text(f"-- Migration: {name}\n-- Target: Supabase\n-- Up migration\n\n")
            sql_down_file.write_text(f"-- Migration: {name}\n-- Target: Supabase\n-- Down migration\n\n")

        if target_database in ['vector', 'all']:
            py_file = self.migrations_dir / f"{migration_id}_vector.py"
            py_template = f'''#!/usr/bin/env python3
"""
Vector database migration: {name}
{description}
"""

from src.ai_coding_agent.services.vector_db import VectorDBClient

def migrate_up(vector_db: VectorDBClient):
    """Apply vector database migration."""
    print(f"Applying vector migration: {name}")
    # Add your migration code here
    pass

def migrate_down(vector_db: VectorDBClient):
    """Rollback vector database migration."""
    print(f"Rolling back vector migration: {name}")
    # Add your rollback code here
    pass
'''
            py_file.write_text(py_template)

        print(f"✅ Created migration: {migration_id}")
        print(f"   📁 Files created in: {self.migrations_dir}")

        return migration_id

    def list_migrations(self) -> List[Migration]:
        """List all available migrations."""
        migrations = []

        for migration_file in self.migrations_dir.glob("*.json"):
            if not migration_file.name.endswith("_applied.json"):
                with open(migration_file, 'r') as f:
                    data = json.load(f)
                    migrations.append(Migration(**data))

        return sorted(migrations, key=lambda m: m.id)

    def get_applied_migrations(self, database: str) -> List[str]:
        """Get list of applied migrations for a database."""
        file_map = {
            'local': self.local_migrations_file,
            'supabase': self.supabase_migrations_file,
            'vector': self.vector_migrations_file
        }

        migrations_file = file_map.get(database)
        if not migrations_file or not migrations_file.exists():
            return []

        with open(migrations_file, 'r') as f:
            return json.load(f)

    def mark_migration_applied(self, migration_id: str, database: str):
        """Mark a migration as applied for a database."""
        applied = self.get_applied_migrations(database)
        if migration_id not in applied:
            applied.append(migration_id)
            applied.sort()

            file_map = {
                'local': self.local_migrations_file,
                'supabase': self.supabase_migrations_file,
                'vector': self.vector_migrations_file
            }

            migrations_file = file_map[database]
            with open(migrations_file, 'w') as f:
                json.dump(applied, f, indent=2)

    def get_pending_migrations(self, database: str) -> List[Migration]:
        """Get pending migrations for a database."""
        all_migrations = self.list_migrations()
        applied = self.get_applied_migrations(database)

        pending = []
        for migration in all_migrations:
            if (migration.target_database in [database, 'all'] and
                migration.id not in applied):
                pending.append(migration)

        return pending

    def apply_migration(self, migration_id: str, database: str):
        """Apply a specific migration to a database."""
        migrations = self.list_migrations()
        migration = next((m for m in migrations if m.id == migration_id), None)

        if not migration:
            print(f"❌ Migration not found: {migration_id}")
            return False

        if migration.target_database not in [database, 'all']:
            print(f"❌ Migration {migration_id} not applicable to {database}")
            return False

        print(f"🔄 Applying migration {migration_id} to {database}...")

        try:
            if database == 'local':
                self._apply_local_migration(migration)
            elif database == 'supabase':
                self._apply_supabase_migration(migration)
            elif database == 'vector':
                self._apply_vector_migration(migration)

            self.mark_migration_applied(migration_id, database)
            print(f"   ✅ Migration applied successfully")
            return True

        except Exception as e:
            print(f"   ❌ Migration failed: {e}")
            return False

    def _apply_local_migration(self, migration: Migration):
        """Apply migration to local database."""
        from src.ai_coding_agent.models.base import get_hybrid_db_manager

        manager = get_hybrid_db_manager()        # Read SQL file
        sql_file = self.migrations_dir / f"{migration.id}_local_up.sql"
        if sql_file.exists():
            sql_content = sql_file.read_text()

            # Execute SQL
            with manager.local_engine.connect() as conn:
                # Split and execute each statement
                statements = [s.strip() for s in sql_content.split(';') if s.strip()]
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        from sqlalchemy import text
                        conn.execute(text(statement))
                conn.commit()

    def _apply_supabase_migration(self, migration: Migration):
        """Apply migration to Supabase."""
        print(f"   ⚠️ Supabase migrations require manual application")
        print(f"   📄 SQL file: {migration.id}_supabase_up.sql")

    def _apply_vector_migration(self, migration: Migration):
        """Apply migration to vector database."""
        from src.ai_coding_agent.services.vector_db import VectorDBClient
        import importlib.util

        # Load migration script
        py_file = self.migrations_dir / f"{migration.id}_vector.py"
        if py_file.exists():
            spec = importlib.util.spec_from_file_location("migration", py_file)
            if spec is not None and spec.loader is not None:
                migration_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(migration_module)

                # Apply migration
                vector_db = VectorDBClient()
                migration_module.migrate_up(vector_db)
            else:
                print(f"   ⚠️ Failed to load migration script: {py_file}")
                return False

    def show_status(self):
        """Show migration status for all databases."""
        print("📊 Migration Status")
        print("=" * 50)

        databases = ['local', 'supabase', 'vector']

        for db in databases:
            applied = self.get_applied_migrations(db)
            pending = self.get_pending_migrations(db)

            print(f"\n🗄️ {db.title()} Database:")
            print(f"   ✅ Applied: {len(applied)} migrations")
            print(f"   ⏳ Pending: {len(pending)} migrations")

            if pending:
                print("   📋 Pending migrations:")
                for migration in pending[:3]:  # Show first 3
                    print(f"      • {migration.id}: {migration.name}")
                if len(pending) > 3:
                    print(f"      ... and {len(pending) - 3} more")

def main():
    """CLI interface for migration management."""
    import sys

    manager = MigrationManager()

    if len(sys.argv) < 2:
        print("🔧 Migration Manager")
        print("Usage:")
        print("  python migration_manager.py create <name> [database]")
        print("  python migration_manager.py status")
        print("  python migration_manager.py apply <migration_id> <database>")
        print("  python migration_manager.py pending <database>")
        return

    command = sys.argv[1]

    if command == "create" and len(sys.argv) >= 3:
        name = sys.argv[2]
        database = sys.argv[3] if len(sys.argv) > 3 else "all"
        description = f"Migration for {name}"
        manager.create_migration(name, description, database)

    elif command == "status":
        manager.show_status()

    elif command == "apply" and len(sys.argv) == 4:
        migration_id = sys.argv[2]
        database = sys.argv[3]
        manager.apply_migration(migration_id, database)

    elif command == "pending" and len(sys.argv) == 3:
        database = sys.argv[2]
        pending = manager.get_pending_migrations(database)
        print(f"📋 Pending migrations for {database}:")
        for migration in pending:
            print(f"   • {migration.id}: {migration.name}")

    else:
        print("❌ Invalid command")

if __name__ == "__main__":
    main()
