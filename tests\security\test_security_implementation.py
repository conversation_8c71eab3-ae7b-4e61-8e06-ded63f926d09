#!/usr/bin/env python3
"""
Security Implementation Test Suite

This script tests the security measures implemented for the AI Coding Agent admin dashboard.
Run this after implementing the security fixes to verify everything is working correctly.
"""

import asyncio
import httpx
import json
import time
from pathlib import Path
import sys
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the src directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ai_coding_agent.services.secure_config import SecureConfigManager
    from ai_coding_agent.services.auth import create_admin_access_token, verify_admin_token
    from ai_coding_agent.middleware.rate_limiting import admin_rate_limiter
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


class SecurityTester:
    """Test suite for security implementations."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results = []

    def log_result(self, test_name: str, passed: bool, message: str):
        """Log a test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {test_name} - {message}")
        self.results.append({
            "test": test_name,
            "passed": passed,
            "message": message
        })

    def test_encryption_system(self):
        """Test API key encryption and decryption."""
        print("\n🔐 Testing API Key Encryption System")
        print("-" * 40)

        try:
            manager = SecureConfigManager()

            # Test basic encryption/decryption
            test_key = "sk-test-key-1234567890abcdef"
            encrypted = manager.encrypt_api_key(test_key, "test_provider")
            decrypted = manager.decrypt_api_key(encrypted, "test_provider")

            if test_key == decrypted:
                self.log_result("Basic Encryption", True, "API key encrypted and decrypted successfully")
            else:
                self.log_result("Basic Encryption", False, f"Decryption mismatch: {test_key} != {decrypted}")

            # Test empty key handling
            try:
                manager.encrypt_api_key("", "test")
                self.log_result("Empty Key Validation", False, "Should reject empty keys")
            except ValueError:
                self.log_result("Empty Key Validation", True, "Correctly rejects empty keys")

            # Test short key handling
            try:
                manager.encrypt_api_key("short", "test")
                self.log_result("Short Key Validation", False, "Should reject short keys")
            except ValueError:
                self.log_result("Short Key Validation", True, "Correctly rejects short keys")

        except Exception as e:
            self.log_result("Encryption System", False, f"Exception: {str(e)}")

    def test_admin_tokens(self):
        """Test admin token creation and validation."""
        print("\n🎫 Testing Admin Token System")
        print("-" * 40)

        try:
            # Test admin token creation
            test_data = {"sub": "test_admin", "username": "test_admin"}
            admin_token = create_admin_access_token(test_data)

            if admin_token:
                self.log_result("Admin Token Creation", True, "Admin token created successfully")
            else:
                self.log_result("Admin Token Creation", False, "Failed to create admin token")
                return

            # Test admin token verification
            payload = verify_admin_token(admin_token)

            if payload and payload.get("admin_session") and payload.get("token_type") == "admin_access":
                self.log_result("Admin Token Verification", True, "Admin token verified successfully")
            else:
                self.log_result("Admin Token Verification", False, "Admin token verification failed")

            # Test token expiration (admin tokens should be short-lived)
            exp_time = payload.get("exp") if payload else None
            iat_time = payload.get("iat") if payload else None

            if exp_time and iat_time:
                duration = exp_time - iat_time
                if duration <= 900:  # 15 minutes = 900 seconds
                    self.log_result("Admin Token Duration", True, f"Token expires in {duration} seconds (≤15 min)")
                else:
                    self.log_result("Admin Token Duration", False, f"Token duration too long: {duration} seconds")
            else:
                self.log_result("Admin Token Duration", False, "Could not determine token duration")

        except Exception as e:
            self.log_result("Admin Token System", False, f"Exception: {str(e)}")

    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        print("\n🚦 Testing Rate Limiting System")
        print("-" * 40)

        try:
            # Test rate limiter directly
            test_ip = "*************"

            # Should allow initial requests
            allowed = admin_rate_limiter.is_allowed(test_ip, max_requests=3, window_minutes=1)
            if allowed:
                self.log_result("Rate Limit Initial", True, "Initial request allowed")
            else:
                self.log_result("Rate Limit Initial", False, "Initial request blocked unexpectedly")

            # Fill up the rate limit
            for i in range(3):
                admin_rate_limiter.is_allowed(test_ip, max_requests=3, window_minutes=1)

            # Should now be blocked
            blocked = not admin_rate_limiter.is_allowed(test_ip, max_requests=3, window_minutes=1)
            if blocked:
                self.log_result("Rate Limit Blocking", True, "Requests blocked after limit exceeded")
            else:
                self.log_result("Rate Limit Blocking", False, "Rate limit not enforced")

            # Test remaining requests calculation
            remaining = admin_rate_limiter.get_remaining_requests(test_ip, max_requests=3)
            if remaining == 0:
                self.log_result("Rate Limit Counting", True, f"Correct remaining count: {remaining}")
            else:
                self.log_result("Rate Limit Counting", False, f"Incorrect remaining count: {remaining}")

        except Exception as e:
            self.log_result("Rate Limiting System", False, f"Exception: {str(e)}")

    async def test_admin_endpoints(self):
        """Test admin endpoint security (requires running server)."""
        print("\n🌐 Testing Admin Endpoint Security")
        print("-" * 40)

        try:
            async with httpx.AsyncClient() as client:
                # Test unauthenticated access
                response = await client.get(f"{self.base_url}/api/v1/admin/auth/check")

                if response.status_code == 401:
                    self.log_result("Unauthenticated Access", True, "Admin endpoint correctly rejects unauthenticated requests")
                else:
                    self.log_result("Unauthenticated Access", False, f"Expected 401, got {response.status_code}")

                # Test rate limiting on auth endpoint
                auth_responses = []
                for i in range(7):  # Exceed the 5-request limit
                    resp = await client.get(f"{self.base_url}/api/v1/admin/auth/check")
                    auth_responses.append(resp.status_code)
                    time.sleep(0.1)  # Small delay

                # Should get 429 (Too Many Requests) after 5 attempts
                if 429 in auth_responses:
                    self.log_result("Auth Rate Limiting", True, "Rate limiting active on auth endpoints")
                else:
                    self.log_result("Auth Rate Limiting", False, f"No rate limiting detected: {auth_responses}")

        except httpx.ConnectError:
            self.log_result("Admin Endpoints", False, "Could not connect to server - is it running?")
        except Exception as e:
            self.log_result("Admin Endpoints", False, f"Exception: {str(e)}")

    def test_environment_config(self):
        """Test environment configuration."""
        print("\n⚙️ Testing Environment Configuration")
        print("-" * 40)

        # Check for required environment variables
        required_vars = [
            "CONFIG_ENCRYPTION_KEY",
            "SECURITY_SECRET_KEY"
        ]

        for var in required_vars:
            value = os.getenv(var)
            if value and len(value) >= 32:
                self.log_result(f"Environment {var}", True, f"Variable set with adequate length ({len(value)} chars)")
            elif value:
                self.log_result(f"Environment {var}", False, f"Variable too short ({len(value)} chars, need ≥32)")
            else:
                self.log_result(f"Environment {var}", False, "Variable not set")

        # Check .env file exists
        env_file = Path(".env")
        if env_file.exists():
            self.log_result("Environment File", True, f".env file found at {env_file.absolute()}")
        else:
            self.log_result("Environment File", False, ".env file not found")

    def print_summary(self):
        """Print test summary."""
        print("\n" + "="*50)
        print("🔒 SECURITY TEST SUMMARY")
        print("="*50)

        passed = sum(1 for r in self.results if r["passed"])
        total = len(self.results)

        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")

        if passed == total:
            print("\n🎉 All security tests passed! Your admin dashboard is secure.")
        else:
            print("\n⚠️ Some security tests failed. Please review and fix the issues above.")

            failed_tests = [r for r in self.results if not r["passed"]]
            print("\nFailed Tests:")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['message']}")

        print("\n📋 Next Steps:")
        print("1. Fix any failed tests")
        print("2. Run this test suite again")
        print("3. Monitor security logs regularly")
        print("4. Set up production monitoring")
        print("5. Schedule regular security reviews")


async def main():
    """Run all security tests."""
    print("🔒 AI Coding Agent Security Test Suite")
    print("="*50)

    tester = SecurityTester()

    # Run all tests
    tester.test_environment_config()
    tester.test_encryption_system()
    tester.test_admin_tokens()
    tester.test_rate_limiting()
    await tester.test_admin_endpoints()

    # Print summary
    tester.print_summary()


if __name__ == "__main__":
    asyncio.run(main())
