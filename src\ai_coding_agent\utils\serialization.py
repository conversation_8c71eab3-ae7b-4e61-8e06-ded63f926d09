"""
JSON Serialization Utilities
Provides bulletproof JSON serialization for complex Python objects.
"""

import json
from datetime import datetime, date
from decimal import Decimal
from typing import Any
from uuid import UUID


def serialize_for_json(obj: Any) -> Any:
    """
    Recursively convert any non-JSON-serializable Python objects into
    JSON-safe formats. This is the bulletproof serializer that handles
    everything the AI Coding Agent project might throw at it.
    
    Handles:
    - datetime/date → ISO format strings
    - Decimal → float or int
    - UUID → string representation
    - bytes → UTF-8 decoded strings
    - Pydantic models (v1 & v2) → dict representation
    - Custom objects → dict or string fallback
    - Nested structures → recursively processed
    - Sets → lists
    - Complex objects → safe string representation
    
    Args:
        obj: Any Python object to serialize
        
    Returns:
        JSON-serializable representation of the object
    """
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj) if obj % 1 else int(obj)
    elif isinstance(obj, UUID):
        return str(obj)
    elif isinstance(obj, bytes):
        return obj.decode("utf-8", errors="replace")
    elif hasattr(obj, 'dict') and callable(getattr(obj, 'dict')):
        # Handle Pydantic v1 models
        return serialize_for_json(obj.dict())
    elif hasattr(obj, 'model_dump') and callable(getattr(obj, 'model_dump')):
        # Handle Pydantic v2 models
        return serialize_for_json(obj.model_dump())
    elif hasattr(obj, '__dict__'):
        # Handle custom objects with __dict__
        return serialize_for_json(obj.__dict__)
    elif isinstance(obj, dict):
        return {k: serialize_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_json(v) for v in obj]
    elif isinstance(obj, tuple):
        return tuple(serialize_for_json(v) for v in obj)
    elif isinstance(obj, set):
        return list(serialize_for_json(v) for v in obj)
    elif obj is None or isinstance(obj, (int, float, str, bool)):
        return obj
    else:
        # Fallback: try stringifying
        try:
            return str(obj)
        except Exception:
            return f"<non-serializable: {type(obj).__name__}>"


def to_json(data: Any, **kwargs) -> str:
    """
    Convert any Python object to a JSON string safely.
    
    Args:
        data: Any Python object to convert to JSON
        **kwargs: Additional arguments to pass to json.dumps()
        
    Returns:
        JSON string representation
    """
    return json.dumps(serialize_for_json(data), **kwargs)


def safe_json_dump(data: Any, indent: int = 2, ensure_ascii: bool = False) -> str:
    """
    Safely dump any Python object to a pretty-printed JSON string.
    
    Args:
        data: Any Python object to serialize
        indent: Number of spaces for indentation
        ensure_ascii: Whether to escape non-ASCII characters
        
    Returns:
        Pretty-printed JSON string
    """
    return to_json(data, indent=indent, ensure_ascii=ensure_ascii)


def prepare_for_logging(obj: Any) -> Any:
    """
    Prepare an object for logging by ensuring it's JSON-serializable.
    Alias for serialize_for_json with a more descriptive name for logging contexts.
    
    Args:
        obj: Object to prepare for logging
        
    Returns:
        JSON-serializable representation
    """
    return serialize_for_json(obj)


def prepare_for_audit(obj: Any) -> Any:
    """
    Prepare an object for audit trail storage by ensuring it's JSON-serializable.
    Alias for serialize_for_json with a more descriptive name for audit contexts.
    
    Args:
        obj: Object to prepare for audit storage
        
    Returns:
        JSON-serializable representation
    """
    return serialize_for_json(obj)


# Convenience functions for specific use cases
def serialize_dependency_data(dependency_data: Any) -> Any:
    """Serialize dependency engine data for storage."""
    return serialize_for_json(dependency_data)


def serialize_ai_response(ai_response: Any) -> Any:
    """Serialize AI orchestrator responses for logging."""
    return serialize_for_json(ai_response)


def serialize_task_data(task_data: Any) -> Any:
    """Serialize task/roadmap data for audit trails."""
    return serialize_for_json(task_data)


def serialize_user_data(user_data: Any) -> Any:
    """Serialize user-related data for audit trails."""
    return serialize_for_json(user_data)


# Export the main functions for easy importing
__all__ = [
    'serialize_for_json',
    'to_json',
    'safe_json_dump',
    'prepare_for_logging',
    'prepare_for_audit',
    'serialize_dependency_data',
    'serialize_ai_response',
    'serialize_task_data',
    'serialize_user_data'
]
