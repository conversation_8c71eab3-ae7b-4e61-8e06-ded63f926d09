"""
Environment isolation validation and testing.

This module provides functionality to validate and test virtual environment
isolation to ensure no cross-contamination between projects.
"""

import subprocess
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging

from .venv_manager import <PERSON>env<PERSON>ana<PERSON>, VenvInfo, get_venv_manager

logger = logging.getLogger(__name__)


class EnvironmentIsolation:
    """Environment isolation validation and testing."""

    def __init__(self):
        self.venv_manager = get_venv_manager()

    def validate_environment_isolation(self, project_names: List[str]) -> Dict[str, Any]:
        """
        Validate that virtual environments are properly isolated.

        Args:
            project_names: List of project names to test

        Returns:
            Dict[str, Any]: Validation results with detailed information
        """
        results = {
            "overall_status": "unknown",
            "projects_tested": len(project_names),
            "isolation_tests": {},
            "cross_contamination_detected": False,
            "errors": []
        }

        try:
            if len(project_names) < 2:
                results["errors"].append("Need at least 2 projects to test isolation")
                results["overall_status"] = "insufficient_data"
                return results

            # Test each project's environment
            for project_name in project_names:
                project_results = self._test_project_isolation(project_name)
                results["isolation_tests"][project_name] = project_results

                if not project_results["isolated"]:
                    results["cross_contamination_detected"] = True

            # Test cross-contamination between projects
            contamination_results = self._test_cross_contamination(project_names)
            results["cross_contamination_tests"] = contamination_results

            if contamination_results["contamination_detected"]:
                results["cross_contamination_detected"] = True

            # Determine overall status
            if results["cross_contamination_detected"]:
                results["overall_status"] = "failed"
            elif results["errors"]:
                results["overall_status"] = "error"
            else:
                results["overall_status"] = "passed"

            logger.info(f"Environment isolation validation completed: {results['overall_status']}")
            return results

        except Exception as e:
            logger.error(f"Environment isolation validation failed: {e}")
            results["errors"].append(str(e))
            results["overall_status"] = "error"
            return results

    def _test_project_isolation(self, project_name: str) -> Dict[str, Any]:
        """Test isolation for a specific project."""
        results = {
            "project_name": project_name,
            "venv_exists": False,
            "isolated": False,
            "python_path_correct": False,
            "pip_path_correct": False,
            "package_isolation": False,
            "errors": []
        }

        try:
            # Get venv info
            venv_info = self.venv_manager.get_venv_info(project_name)
            if not venv_info:
                results["errors"].append("Virtual environment not found")
                return results

            results["venv_exists"] = True

            # Test Python executable path
            results["python_path_correct"] = self._test_python_path(venv_info)

            # Test pip executable path
            results["pip_path_correct"] = self._test_pip_path(venv_info)

            # Test package isolation
            results["package_isolation"] = self._test_package_isolation(venv_info)

            # Overall isolation status
            results["isolated"] = (
                results["python_path_correct"] and
                results["pip_path_correct"] and
                results["package_isolation"]
            )

        except Exception as e:
            logger.error(f"Project isolation test failed for {project_name}: {e}")
            results["errors"].append(str(e))

        return results

    def _test_python_path(self, venv_info: VenvInfo) -> bool:
        """Test that Python executable is from the virtual environment."""
        try:
            # Run Python and check sys.executable
            cmd = [str(venv_info.python_executable), "-c", "import sys; print(sys.executable)"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            reported_executable = Path(result.stdout.strip())
            expected_executable = venv_info.python_executable.resolve()

            # Check if paths match (resolve to handle symlinks)
            paths_match = reported_executable.resolve() == expected_executable

            if not paths_match:
                logger.warning(f"Python path mismatch: expected {expected_executable}, got {reported_executable}")

            return paths_match

        except Exception as e:
            logger.error(f"Python path test failed: {e}")
            return False

    def _test_pip_path(self, venv_info: VenvInfo) -> bool:
        """Test that pip is from the virtual environment."""
        try:
            # Check pip location
            cmd = [str(venv_info.pip_executable), "--version"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            # Pip version output includes path information
            pip_output = result.stdout.strip()
            venv_path_str = str(venv_info.path)

            # Check if pip is using the venv path
            return venv_path_str in pip_output

        except Exception as e:
            logger.error(f"Pip path test failed: {e}")
            return False

    def _test_package_isolation(self, venv_info: VenvInfo) -> bool:
        """Test that packages are isolated to the virtual environment."""
        try:
            # Install a test package that's unlikely to be in system Python
            test_package = "pip-autoremove"  # Small utility package

            # Check if package is already installed
            initial_packages = self.venv_manager.list_packages(venv_info)
            package_already_installed = test_package in initial_packages

            if not package_already_installed:
                # Install test package
                success = self.venv_manager.install_packages(venv_info, [test_package])
                if not success:
                    logger.warning("Failed to install test package for isolation test")
                    return False

            # Verify package is available in venv
            cmd = [str(venv_info.python_executable), "-c", f"import {test_package.replace('-', '_')}; print('success')"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)

            package_available = result.returncode == 0 and "success" in result.stdout

            # Clean up if we installed the package
            if not package_already_installed and package_available:
                try:
                    uninstall_cmd = [str(venv_info.pip_executable), "uninstall", test_package, "-y"]
                    subprocess.run(uninstall_cmd, capture_output=True, text=True, check=False)
                except Exception:
                    pass  # Cleanup failure is not critical

            return package_available

        except Exception as e:
            logger.error(f"Package isolation test failed: {e}")
            return False

    def _test_cross_contamination(self, project_names: List[str]) -> Dict[str, Any]:
        """Test for cross-contamination between project environments."""
        results = {
            "contamination_detected": False,
            "test_results": {},
            "errors": []
        }

        try:
            # Install different test packages in different environments
            test_packages = ["requests", "click", "colorama"]

            for i, project_name in enumerate(project_names[:3]):  # Test up to 3 projects
                if i >= len(test_packages):
                    break

                venv_info = self.venv_manager.get_venv_info(project_name)
                if not venv_info:
                    continue

                test_package = test_packages[i]

                # Install unique package in this environment
                success = self.venv_manager.install_packages(venv_info, [test_package])
                if not success:
                    results["errors"].append(f"Failed to install {test_package} in {project_name}")
                    continue

                # Test that other environments don't have this package
                for j, other_project in enumerate(project_names):
                    if i == j or other_project == project_name:
                        continue

                    other_venv_info = self.venv_manager.get_venv_info(other_project)
                    if not other_venv_info:
                        continue

                    # Check if package is available in other environment
                    cmd = [str(other_venv_info.python_executable), "-c",
                          f"try:\n    import {test_package}\n    print('contaminated')\nexcept ImportError:\n    print('isolated')"]

                    result = subprocess.run(cmd, capture_output=True, text=True, check=False)

                    if "contaminated" in result.stdout:
                        results["contamination_detected"] = True
                        contamination_key = f"{project_name}_to_{other_project}"
                        results["test_results"][contamination_key] = {
                            "package": test_package,
                            "contaminated": True
                        }
                        logger.warning(f"Cross-contamination detected: {test_package} from {project_name} found in {other_project}")
                    else:
                        isolation_key = f"{project_name}_to_{other_project}"
                        results["test_results"][isolation_key] = {
                            "package": test_package,
                            "contaminated": False
                        }

        except Exception as e:
            logger.error(f"Cross-contamination test failed: {e}")
            results["errors"].append(str(e))

        return results

    def perform_health_check(self, project_name: str) -> Dict[str, Any]:
        """
        Perform a comprehensive health check on a project's virtual environment.

        Args:
            project_name: Name of the project to check

        Returns:
            Dict[str, Any]: Health check results
        """
        results = {
            "project_name": project_name,
            "overall_health": "unknown",
            "venv_exists": False,
            "executables_valid": False,
            "pip_functional": False,
            "python_functional": False,
            "package_count": 0,
            "issues": [],
            "recommendations": []
        }

        try:
            # Check if venv exists
            venv_info = self.venv_manager.get_venv_info(project_name)
            if not venv_info:
                results["issues"].append("Virtual environment does not exist")
                results["recommendations"].append("Create virtual environment using create_project_venv()")
                results["overall_health"] = "critical"
                return results

            results["venv_exists"] = True

            # Check executables
            if venv_info.python_executable.exists() and venv_info.pip_executable.exists():
                results["executables_valid"] = True
            else:
                results["issues"].append("Python or pip executable missing")
                results["recommendations"].append("Recreate virtual environment")

            # Test Python functionality
            try:
                cmd = [str(venv_info.python_executable), "-c", "print('test')"]
                result = subprocess.run(cmd, capture_output=True, text=True, check=True, timeout=10)
                if "test" in result.stdout:
                    results["python_functional"] = True
            except Exception as e:
                results["issues"].append(f"Python executable not functional: {e}")
                results["recommendations"].append("Recreate virtual environment")

            # Test pip functionality
            try:
                cmd = [str(venv_info.pip_executable), "--version"]
                result = subprocess.run(cmd, capture_output=True, text=True, check=True, timeout=10)
                if "pip" in result.stdout:
                    results["pip_functional"] = True
            except Exception as e:
                results["issues"].append(f"Pip not functional: {e}")
                results["recommendations"].append("Reinstall pip in virtual environment")

            # Count packages
            packages = self.venv_manager.list_packages(venv_info)
            results["package_count"] = len(packages)

            # Determine overall health
            if results["executables_valid"] and results["python_functional"] and results["pip_functional"]:
                results["overall_health"] = "healthy"
            elif results["venv_exists"] and (results["python_functional"] or results["pip_functional"]):
                results["overall_health"] = "degraded"
            else:
                results["overall_health"] = "critical"

        except Exception as e:
            logger.error(f"Health check failed for {project_name}: {e}")
            results["issues"].append(str(e))
            results["overall_health"] = "error"

        return results


# Global isolation manager instance
_isolation_manager: Optional[EnvironmentIsolation] = None


def get_isolation_manager() -> EnvironmentIsolation:
    """Get the global environment isolation manager."""
    global _isolation_manager
    if _isolation_manager is None:
        _isolation_manager = EnvironmentIsolation()
    return _isolation_manager


def validate_environment_isolation(project_names: List[str]) -> Dict[str, Any]:
    """
    Validate environment isolation using the global manager.

    Args:
        project_names: List of project names to test

    Returns:
        Dict[str, Any]: Validation results
    """
    return get_isolation_manager().validate_environment_isolation(project_names)
