#!/usr/bin/env python3
"""
Setup and verification script for AI Coding Agent with CUDA support.

This script helps set up the multi-agent system with proper CUDA configuration
for optimal performance on NVIDIA hardware.
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def run_command(cmd, check=True):
    """Run a command and return the result."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except subprocess.CalledProcessError as e:
        return e.stdout, e.stderr, e.returncode

def check_nvidia_driver():
    """Check if NVIDIA drivers are installed."""
    print("🔍 Checking NVIDIA drivers...")
    stdout, stderr, code = run_command("nvidia-smi", check=False)

    if code == 0:
        print("✅ NVIDIA drivers detected")
        print(f"   {stdout.split(chr(10))[0]}")  # First line with driver info
        return True
    else:
        print("❌ NVIDIA drivers not found")
        print("   Please install NVIDIA drivers from: https://www.nvidia.com/drivers/")
        return False

def check_cuda_toolkit():
    """Check if CUDA toolkit is available."""
    print("\n🔍 Checking CUDA toolkit...")

    try:
        import torch
        if torch.cuda.is_available():
            print("✅ CUDA toolkit detected via PyTorch")
            print(f"   CUDA Version: {torch.version.cuda}")
            print(f"   Device: {torch.cuda.get_device_name(0)}")
            print(f"   Device Count: {torch.cuda.device_count()}")
            return True
        else:
            print("❌ CUDA not available via PyTorch")
            return False
    except ImportError:
        print("❌ PyTorch not installed")
        return False

def check_conda_environment():
    """Check if we're in the correct conda environment."""
    print("\n🔍 Checking conda environment...")

    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env == 'ai-coding-agent':
        print("✅ In correct conda environment: ai-coding-agent")
        return True
    else:
        print(f"⚠️  Current environment: {conda_env}")
        print("   Please activate the ai-coding-agent environment:")
        print("   conda activate ai-coding-agent")
        return False

def check_ollama_installation():
    """Check if Ollama is installed and running."""
    print("\n🔍 Checking Ollama installation...")

    # Check if ollama command exists
    stdout, stderr, code = run_command("ollama --version", check=False)
    if code != 0:
        print("❌ Ollama not installed")
        print("   Install from: https://ollama.ai/download")
        return False

    print(f"✅ Ollama installed: {stdout}")

    # Check if Ollama server is running
    stdout, stderr, code = run_command("ollama list", check=False)
    if code != 0:
        print("⚠️  Ollama server not running")
        print("   Starting Ollama server...")
        subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(3)

        # Check again
        stdout, stderr, code = run_command("ollama list", check=False)
        if code == 0:
            print("✅ Ollama server started")
        else:
            print("❌ Failed to start Ollama server")
            return False
    else:
        print("✅ Ollama server running")

    return True

def verify_models():
    """Verify that required models are available."""
    print("\n🔍 Checking Ollama models...")

    required_models = [
        "llama3.2:3b",
        "starcoder2:3b",
        "deepseek-coder:6.7b-instruct",
        "qwen2.5:3b",
        "yi-coder:1.5b",
        "mistral:7b-instruct-q4_0"
    ]

    stdout, stderr, code = run_command("ollama list", check=False)
    if code != 0:
        print("❌ Cannot list Ollama models")
        return False

    available_models = [line.split()[0] for line in stdout.split('\n')[1:] if line.strip()]

    missing_models = []
    for model in required_models:
        if model in available_models:
            print(f"✅ {model}")
        else:
            print(f"❌ {model} - not found")
            missing_models.append(model)

    if missing_models:
        print(f"\n⚠️  Missing {len(missing_models)} models. To install:")
        for model in missing_models:
            print(f"   ollama pull {model}")
        return False

    return True

def test_gpu_acceleration():
    """Test GPU acceleration with a simple model inference."""
    print("\n🔍 Testing GPU acceleration...")

    try:
        import httpx
        import asyncio
        import json

        async def test_inference():
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json={
                        "model": "llama3.2:3b",
                        "prompt": "Hello, test GPU",
                        "stream": False,
                        "options": {"max_tokens": 10}
                    },
                    timeout=30.0
                )
                return response.status_code == 200, response.json()

        start_time = time.time()
        success, result = asyncio.run(test_inference())
        end_time = time.time()

        if success:
            print(f"✅ GPU acceleration test passed")
            print(f"   Response time: {end_time - start_time:.2f}s")
            if end_time - start_time < 10:
                print("   🚀 Performance looks good (GPU likely active)")
            else:
                print("   ⚠️  Slow response (may be using CPU)")
        else:
            print("❌ GPU acceleration test failed")

        return success

    except Exception as e:
        print(f"❌ GPU test error: {e}")
        return False

def create_performance_benchmark():
    """Create a benchmark script for ongoing performance monitoring."""
    print("\n📝 Creating performance benchmark script...")

    benchmark_script = '''#!/usr/bin/env python3
"""
Performance benchmark for AI Coding Agent models.
Run this periodically to ensure GPU acceleration is working.
"""

import asyncio
import time
import httpx
import statistics

async def benchmark_model(model_name, prompt="Write a simple Python function"):
    """Benchmark a single model."""
    times = []

    async with httpx.AsyncClient() as client:
        for i in range(3):  # 3 runs for average
            start = time.time()
            try:
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json={
                        "model": model_name,
                        "prompt": prompt,
                        "stream": False,
                        "options": {"max_tokens": 50}
                    },
                    timeout=60.0
                )
                end = time.time()
                if response.status_code == 200:
                    times.append(end - start)
                else:
                    print(f"❌ {model_name}: HTTP {response.status_code}")
                    return None
            except Exception as e:
                print(f"❌ {model_name}: {e}")
                return None

    if times:
        avg_time = statistics.mean(times)
        return avg_time
    return None

async def main():
    """Run benchmark on all models."""
    models = [
        "llama3.2:3b",
        "starcoder2:3b",
        "yi-coder:1.5b"
    ]

    print("🚀 Running performance benchmark...")
    print("=" * 50)

    results = {}
    for model in models:
        print(f"Testing {model}...")
        avg_time = await benchmark_model(model)
        if avg_time:
            results[model] = avg_time
            status = "🚀 Excellent" if avg_time < 5 else "✅ Good" if avg_time < 15 else "⚠️ Slow"
            print(f"  {status}: {avg_time:.2f}s average")
        else:
            print(f"  ❌ Failed")

    print("=" * 50)
    if results:
        fastest = min(results.items(), key=lambda x: x[1])
        print(f"🏆 Fastest: {fastest[0]} ({fastest[1]:.2f}s)")

        total_avg = statistics.mean(results.values())
        if total_avg < 10:
            print(f"✅ Overall performance: Excellent ({total_avg:.2f}s avg)")
        elif total_avg < 20:
            print(f"⚠️ Overall performance: Good ({total_avg:.2f}s avg)")
        else:
            print(f"❌ Overall performance: Needs improvement ({total_avg:.2f}s avg)")

if __name__ == "__main__":
    asyncio.run(main())
'''

    with open("benchmark_models.py", "w") as f:
        f.write(benchmark_script)

    print("✅ Created benchmark_models.py")

def main():
    """Main setup and verification routine."""
    print("🚀 AI Coding Agent - CUDA Setup & Verification")
    print("=" * 60)

    checks = [
        ("NVIDIA Drivers", check_nvidia_driver),
        ("Conda Environment", check_conda_environment),
        ("CUDA Toolkit", check_cuda_toolkit),
        ("Ollama Installation", check_ollama_installation),
        ("Required Models", verify_models),
        ("GPU Acceleration", test_gpu_acceleration)
    ]

    passed = 0
    total = len(checks)

    for name, check_func in checks:
        try:
            if check_func():
                passed += 1
        except Exception as e:
            print(f"❌ {name}: Error - {e}")

    print(f"\n📊 Setup Status: {passed}/{total} checks passed")

    if passed == total:
        print("🎉 All checks passed! Your system is ready for high-performance AI model orchestration.")
        print("\n📝 Next steps:")
        print("1. Run: python verify_models.py")
        print("2. Run: python run_phase_a2_tests.py")
        print("3. Monitor performance with: python benchmark_models.py")
    else:
        print("⚠️  Some checks failed. Please address the issues above.")
        print("\n🔧 Quick fixes:")
        print("1. Create environment: conda env create -f environment.yml")
        print("2. Activate environment: conda activate ai-coding-agent")
        print("3. Install missing models: ollama pull <model-name>")

    create_performance_benchmark()

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
