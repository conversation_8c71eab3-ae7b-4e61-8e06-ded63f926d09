@echo off
echo Optimizing Ollama for 4GB Quadro P1000...

REM Set environment variables for GPU optimization
set OLLAMA_GPU_LAYERS=999
set OLLAMA_GPU_MEMORY_FRACTION=0.95
set OLLAMA_MAX_LOADED_MODELS=2
set OLLAMA_KEEP_ALIVE=5m
set OLLAMA_FLASH_ATTENTION=1

echo Environment variables set:
echo OLLAMA_GPU_LAYERS=%OLLAMA_GPU_LAYERS%
echo OLLAMA_GPU_MEMORY_FRACTION=%OLLAMA_GPU_MEMORY_FRACTION%
echo OLLAMA_MAX_LOADED_MODELS=%OLLAMA_MAX_LOADED_MODELS%
echo OLLAMA_KEEP_ALIVE=%OLLAMA_KEEP_ALIVE%
echo OLLAMA_FLASH_ATTENTION=%OLLAMA_FLASH_ATTENTION%

echo.
echo Stopping Ollama service...
taskkill /f /im ollama.exe 2>nul

echo Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo Starting Ollama with optimized settings...
start "" ollama serve

echo.
echo Optimization complete!
echo Models should now use 100%% GPU instead of CPU/GPU split.
echo.
pause
