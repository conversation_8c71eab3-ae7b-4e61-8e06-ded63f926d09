"""
Platform detection functionality for cross-platform support.

This module provides platform detection capabilities for Windows, macOS, and Linux
systems with comprehensive error handling and validation.
"""

import platform
import sys
from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class SupportedPlatform(Enum):
    """Enumeration of supported platforms."""
    WINDOWS = "windows"
    MACOS = "macos"
    LINUX = "linux"


@dataclass
class PlatformInfo:
    """Platform information container."""
    platform_type: SupportedPlatform
    system: str
    release: str
    version: str
    machine: str
    processor: str
    python_version: str
    is_64bit: bool


class PlatformDetector:
    """Platform detection and validation class."""

    def __init__(self):
        self._platform_info: Optional[PlatformInfo] = None

    def detect_platform(self) -> PlatformInfo:
        """
        Detect the current platform and return detailed information.

        Returns:
            PlatformInfo: Comprehensive platform information

        Raises:
            RuntimeError: If platform is not supported
        """
        try:
            system = platform.system().lower()

            # Map system names to our supported platforms
            platform_mapping = {
                'windows': SupportedPlatform.WINDOWS,
                'darwin': SupportedPlatform.MACOS,
                'linux': SupportedPlatform.LINUX
            }

            if system not in platform_mapping:
                raise RuntimeError(f"Unsupported platform: {system}")

            platform_type = platform_mapping[system]

            # Gather comprehensive platform information
            self._platform_info = PlatformInfo(
                platform_type=platform_type,
                system=platform.system(),
                release=platform.release(),
                version=platform.version(),
                machine=platform.machine(),
                processor=platform.processor(),
                python_version=sys.version,
                is_64bit=sys.maxsize > 2**32
            )

            logger.info(f"Detected platform: {platform_type.value}")
            return self._platform_info

        except Exception as e:
            logger.error(f"Platform detection failed: {e}")
            raise RuntimeError(f"Failed to detect platform: {e}")

    def get_cached_info(self) -> Optional[PlatformInfo]:
        """Get cached platform information if available."""
        return self._platform_info

    def is_windows(self) -> bool:
        """Check if running on Windows."""
        info = self._platform_info or self.detect_platform()
        return info.platform_type == SupportedPlatform.WINDOWS

    def is_macos(self) -> bool:
        """Check if running on macOS."""
        info = self._platform_info or self.detect_platform()
        return info.platform_type == SupportedPlatform.MACOS

    def is_linux(self) -> bool:
        """Check if running on Linux."""
        info = self._platform_info or self.detect_platform()
        return info.platform_type == SupportedPlatform.LINUX

    def is_unix_like(self) -> bool:
        """Check if running on Unix-like system (macOS or Linux)."""
        info = self._platform_info or self.detect_platform()
        return info.platform_type in [SupportedPlatform.MACOS, SupportedPlatform.LINUX]


# Global detector instance
_detector = PlatformDetector()


def get_platform_info() -> PlatformInfo:
    """
    Get platform information using the global detector instance.

    Returns:
        PlatformInfo: Comprehensive platform information
    """
    return _detector.detect_platform()
