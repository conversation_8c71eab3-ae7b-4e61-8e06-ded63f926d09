"""
Test Phase B1: Roadmap System Implementation
Basic validation of roadmap CRUD operations.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.ai_coding_agent.config import settings
from src.ai_coding_agent.models import (
    Base, Project, Roadmap, Phase, Step, Task,
    TaskStatus, AgentType,
    ProjectCreate, RoadmapCreate, PhaseCreate, StepCreate, TaskCreate
)
from src.ai_coding_agent.services.roadmap import RoadmapService


def test_roadmap_system():
    """Test basic roadmap system functionality."""
    print("🧪 Testing Phase B1: Roadmap System Implementation")

    # Create test database engine
    engine = create_engine(settings.database.url)
    Base.metadata.create_all(engine)

    SessionLocal = sessionmaker(bind=engine)
    db = SessionLocal()

    try:
        service = RoadmapService(db)

        # Test 1: Create a sample project with roadmap
        print("\n1️⃣ Testing project creation with roadmap...")

        sample_roadmap = RoadmapCreate(
            name="Sample Web App Roadmap",
            version="1.0.0",
            phases=[
                PhaseCreate(
                    name="Foundation",
                    description="Basic project setup",
                    order_index=0,
                    estimated_duration="1 week",
                    steps=[
                        StepCreate(
                            name="Environment Setup",
                            description="Set up development environment",
                            order_index=0,
                            tasks=[
                                TaskCreate(
                                    name="Initialize Git Repository",
                                    description="Create Git repo and initial commit",
                                    order_index=0,
                                    assigned_agent=AgentType.SHELL,
                                    estimated_duration="30 minutes"
                                ),
                                TaskCreate(
                                    name="Setup Package.json",
                                    description="Create package.json with dependencies",
                                    order_index=1,
                                    assigned_agent=AgentType.BACKEND,
                                    estimated_duration="1 hour",
                                    dependencies=[]  # Depends on first task
                                )
                            ]
                        ),
                        StepCreate(
                            name="Basic Structure",
                            description="Create basic project structure",
                            order_index=1,
                            tasks=[
                                TaskCreate(
                                    name="Create Frontend Components",
                                    description="Set up React components structure",
                                    order_index=0,
                                    assigned_agent=AgentType.FRONTEND,
                                    estimated_duration="2 hours"
                                )
                            ]
                        )
                    ]
                ),
                PhaseCreate(
                    name="Core Features",
                    description="Implement main application features",
                    order_index=1,
                    estimated_duration="2 weeks",
                    dependencies=[],  # Depends on Foundation phase
                    steps=[
                        StepCreate(
                            name="API Development",
                            description="Create backend APIs",
                            order_index=0,
                            tasks=[
                                TaskCreate(
                                    name="User Authentication API",
                                    description="Implement user auth endpoints",
                                    order_index=0,
                                    assigned_agent=AgentType.BACKEND,
                                    estimated_duration="1 day"
                                )
                            ]
                        )
                    ]
                )
            ]
        )

        project_data = ProjectCreate(
            name="Sample Web Application",
            description="A sample web application for testing roadmap system",
            tech_stack={
                "frontend": "React",
                "backend": "FastAPI",
                "database": "PostgreSQL"
            },
            project_rules={
                "style_profile": {
                    "voice": "professional",
                    "layout": "modern"
                },
                "quality_gates": {
                    "test_coverage_minimum": 80,
                    "code_review_required": True
                }
            },
            roadmap=sample_roadmap
        )

        project = service.create_project(project_data)
        print(f"✅ Created project: {project.name} (ID: {project.id})")

        if project.roadmap:
            print(f"📋 Roadmap phases: {len(project.roadmap.phases)}")
        else:
            print("❌ No roadmap created")
            return False

        # Test 2: Get project progress
        print("\n2️⃣ Testing project progress calculation...")
        progress = service.get_project_progress(project.id)
        print(f"📊 Overall progress: {progress['overall_progress']}%")
        print(f"📈 Total tasks: {progress['total_tasks']}")
        print(f"✅ Completed: {progress['completed_tasks']}")

        # Test 3: Update task status and test status bubbling
        print("\n3️⃣ Testing task status updates and bubbling...")

        # Get first task
        if not project.roadmap or not project.roadmap.phases:
            print("❌ No roadmap phases available")
            return False

        first_phase = project.roadmap.phases[0]
        first_step = first_phase.steps[0]
        first_task = first_step.tasks[0]

        print(f"📝 Updating task: {first_task.name}")

        # Update to in-progress
        updated_task = service.update_task_status(
            first_task.id,
            TaskStatus.IN_PROGRESS
        )
        print(f"🔄 Task status: {updated_task.status}")

        # Complete the task
        updated_task = service.update_task_status(
            first_task.id,
            TaskStatus.COMPLETED,
            artifacts=[{
                "type": "code",
                "filename": ".gitignore",
                "content": "node_modules/\n.env\n",
                "description": "Git ignore file for project"
            }]
        )
        print(f"✅ Task completed with {len(updated_task.artifacts)} artifacts")

        # Check updated progress
        progress = service.get_project_progress(project.id)
        print(f"📊 Updated progress: {progress['overall_progress']}%")

        # Test 4: Test dependency validation (Phase B2 preview)
        print("\n4️⃣ Testing basic dependency checking...")

        # Try to get available tasks
        available_tasks = []
        if project.roadmap:
            for phase in project.roadmap.phases:
                for step in phase.steps:
                    for task in step.tasks:
                        if task.status == TaskStatus.PENDING:
                            # Simple dependency check
                            can_start = True
                            if task.dependencies:
                                print(f"🔒 Task '{task.name}' has dependencies: {task.dependencies}")
                                can_start = False

                            if can_start:
                                available_tasks.append(task.name)

        print(f"🟢 Available tasks: {len(available_tasks)}")
        for task_name in available_tasks:
            print(f"  - {task_name}")

        # Test 5: Test roadmap retrieval
        print("\n5️⃣ Testing roadmap retrieval...")
        retrieved_roadmap = service.get_project_roadmap(project.id)
        print(f"📋 Retrieved roadmap: {retrieved_roadmap.name}")
        print(f"🔄 Status: {retrieved_roadmap.status}")

        print("\n✅ Phase B1 Roadmap System Implementation - Basic tests passed!")
        print("🚀 Ready for Phase B2: Dependency Engine & Phase Locking")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        db.close()


if __name__ == "__main__":
    success = test_roadmap_system()
    exit(0 if success else 1)
