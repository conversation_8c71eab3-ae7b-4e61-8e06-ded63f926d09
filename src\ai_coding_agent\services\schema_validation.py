"""
JSON Schema Validation Service
Implements validation for roadmap.json and other schema files.
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

try:
    import jsonschema
    from jsonschema import validate, ValidationError, Draft7Validator
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

from fastapi import HTTPException
from fastapi import status as http_status


class SchemaValidationService:
    """Service for validating JSON data against schemas."""

    def __init__(self):
        self.schemas_dir = Path(__file__).parent.parent.parent.parent / "schemas"
        self._schema_cache = {}

    def _load_schema(self, schema_name: str) -> Dict:
        """Load a JSON schema from the schemas directory."""
        if schema_name in self._schema_cache:
            return self._schema_cache[schema_name]

        schema_path = self.schemas_dir / f"{schema_name}.json"
        
        if not schema_path.exists():
            raise FileNotFoundError(f"Schema file not found: {schema_path}")

        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            
            # Cache the schema
            self._schema_cache[schema_name] = schema
            return schema
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in schema file {schema_path}: {str(e)}")

    def validate_roadmap(self, roadmap_data: Dict) -> Tuple[bool, List[str]]:
        """
        Validate roadmap data against the roadmap.json schema.
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        if not JSONSCHEMA_AVAILABLE:
            # Fallback to basic validation if jsonschema is not available
            return self._basic_roadmap_validation(roadmap_data)

        try:
            schema = self._load_schema("roadmap")
            validate(instance=roadmap_data, schema=schema)
            return True, []
            
        except ValidationError as e:
            return False, [str(e)]
        except Exception as e:
            return False, [f"Schema validation error: {str(e)}"]

    def validate_roadmap_strict(self, roadmap_data: Dict) -> None:
        """
        Validate roadmap data and raise HTTPException if invalid.
        
        Raises:
            HTTPException: If validation fails
        """
        is_valid, errors = self.validate_roadmap(roadmap_data)
        
        if not is_valid:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Roadmap validation failed",
                    "errors": errors
                }
            )

    def _basic_roadmap_validation(self, roadmap_data: Dict) -> Tuple[bool, List[str]]:
        """
        Basic validation when jsonschema is not available.
        Checks required fields and basic structure.
        """
        errors = []

        # Check required top-level fields
        required_fields = ["project_id", "name", "version", "phases"]
        for field in required_fields:
            if field not in roadmap_data:
                errors.append(f"Missing required field: {field}")

        # Validate project_id format (basic UUID check)
        if "project_id" in roadmap_data:
            project_id = roadmap_data["project_id"]
            if not isinstance(project_id, str) or len(project_id) != 36:
                errors.append("project_id must be a valid UUID string")

        # Validate version format (semantic versioning)
        if "version" in roadmap_data:
            version = roadmap_data["version"]
            if not isinstance(version, str):
                errors.append("version must be a string")
            else:
                parts = version.split(".")
                if len(parts) != 3 or not all(part.isdigit() for part in parts):
                    errors.append("version must follow semantic versioning (major.minor.patch)")

        # Validate phases structure
        if "phases" in roadmap_data:
            phases = roadmap_data["phases"]
            if not isinstance(phases, list) or len(phases) == 0:
                errors.append("phases must be a non-empty array")
            else:
                for i, phase in enumerate(phases):
                    phase_errors = self._validate_phase(phase, f"phases[{i}]")
                    errors.extend(phase_errors)

        return len(errors) == 0, errors

    def _validate_phase(self, phase: Dict, path: str) -> List[str]:
        """Validate a single phase object."""
        errors = []
        required_fields = ["id", "name", "order_index", "steps"]
        
        for field in required_fields:
            if field not in phase:
                errors.append(f"{path}: Missing required field '{field}'")

        # Validate order_index
        if "order_index" in phase:
            if not isinstance(phase["order_index"], int) or phase["order_index"] < 0:
                errors.append(f"{path}: order_index must be a non-negative integer")

        # Validate status
        if "status" in phase:
            valid_statuses = ["pending", "in_progress", "completed", "blocked", "cancelled"]
            if phase["status"] not in valid_statuses:
                errors.append(f"{path}: status must be one of {valid_statuses}")

        # Validate steps
        if "steps" in phase:
            steps = phase["steps"]
            if not isinstance(steps, list) or len(steps) == 0:
                errors.append(f"{path}: steps must be a non-empty array")
            else:
                for i, step in enumerate(steps):
                    step_errors = self._validate_step(step, f"{path}.steps[{i}]")
                    errors.extend(step_errors)

        return errors

    def _validate_step(self, step: Dict, path: str) -> List[str]:
        """Validate a single step object."""
        errors = []
        required_fields = ["id", "name", "order_index", "tasks"]
        
        for field in required_fields:
            if field not in step:
                errors.append(f"{path}: Missing required field '{field}'")

        # Validate order_index
        if "order_index" in step:
            if not isinstance(step["order_index"], int) or step["order_index"] < 0:
                errors.append(f"{path}: order_index must be a non-negative integer")

        # Validate tasks
        if "tasks" in step:
            tasks = step["tasks"]
            if not isinstance(tasks, list) or len(tasks) == 0:
                errors.append(f"{path}: tasks must be a non-empty array")
            else:
                for i, task in enumerate(tasks):
                    task_errors = self._validate_task(task, f"{path}.tasks[{i}]")
                    errors.extend(task_errors)

        return errors

    def _validate_task(self, task: Dict, path: str) -> List[str]:
        """Validate a single task object."""
        errors = []
        required_fields = ["id", "name", "order_index", "assigned_agent"]
        
        for field in required_fields:
            if field not in task:
                errors.append(f"{path}: Missing required field '{field}'")

        # Validate assigned_agent
        if "assigned_agent" in task:
            valid_agents = ["architect", "frontend", "backend", "shell", "issue_fix"]
            if task["assigned_agent"] not in valid_agents:
                errors.append(f"{path}: assigned_agent must be one of {valid_agents}")

        # Validate status
        if "status" in task:
            valid_statuses = ["pending", "in_progress", "completed", "failed", "blocked", "cancelled"]
            if task["status"] not in valid_statuses:
                errors.append(f"{path}: status must be one of {valid_statuses}")

        # Validate artifacts
        if "artifacts" in task:
            artifacts = task["artifacts"]
            if not isinstance(artifacts, list):
                errors.append(f"{path}: artifacts must be an array")
            else:
                for i, artifact in enumerate(artifacts):
                    artifact_errors = self._validate_artifact(artifact, f"{path}.artifacts[{i}]")
                    errors.extend(artifact_errors)

        return errors

    def _validate_artifact(self, artifact: Dict, path: str) -> List[str]:
        """Validate a single artifact object."""
        errors = []
        required_fields = ["type", "name"]
        
        for field in required_fields:
            if field not in artifact:
                errors.append(f"{path}: Missing required field '{field}'")

        # Validate artifact type
        if "type" in artifact:
            valid_types = ["code", "documentation", "configuration", "test", "deployment", "design"]
            if artifact["type"] not in valid_types:
                errors.append(f"{path}: type must be one of {valid_types}")

        return errors

    def get_schema_info(self, schema_name: str) -> Dict:
        """Get information about a schema."""
        try:
            schema = self._load_schema(schema_name)
            return {
                "name": schema_name,
                "title": schema.get("title", "Unknown"),
                "description": schema.get("description", "No description"),
                "version": schema.get("$id", "Unknown"),
                "required_fields": schema.get("required", [])
            }
        except Exception as e:
            return {
                "name": schema_name,
                "error": str(e)
            }

    def list_available_schemas(self) -> List[str]:
        """List all available schema files."""
        if not self.schemas_dir.exists():
            return []
        
        return [
            f.stem for f in self.schemas_dir.glob("*.json")
            if f.is_file()
        ]


# Global instance
schema_validator = SchemaValidationService()
